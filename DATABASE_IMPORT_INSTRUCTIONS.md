# ModernPOS Database Import Instructions

## Overview
The `modernpos_database_complete.sql` file contains:
- Complete database structure (all tables)
- Essential data required to run the system
- Default settings for store and users

## Database Contents

### Main Tables:
1. **Store Management**: `stores`, `settings`
2. **User Management**: `users`, `user_groups`, `user_to_store`
3. **Customer Management**: `customers`, `customer_transactions`
4. **Supplier Management**: `suppliers`
5. **Product Management**: `products`, `product_to_store`, `categorys`, `brands`, `units`
6. **Sales Management**: `selling_info`, `selling_item`, `selling_price`
7. **Purchase Management**: `purchase_info`, `purchase_item`, `purchase_price`
8. **Payment Management**: `payments`, `payment_methods`
9. **Returns Management**: `returns`, `return_items`
10. **Quotation Management**: `quotation_info`, `quotation_item`, `quotation_price`
11. **Holding Orders**: `holding_info`, `holding_item`, `holding_price`
12. **Expense Management**: `expenses`, `expense_categorys`
13. **Banking**: `bank_accounts`, `bank_transaction_info`
14. **Loans**: `loans`
15. **Installments**: `installment_orders`, `installment_payments`
16. **Transfers**: `transfer_items`
17. **Tax Management**: `taxrates`, `taxrate_to_store`
18. **Currency Management**: `currency`, `currency_to_store`
19. **Gift Cards**: `giftcards`
20. **Logs**: `sell_logs`, `purchase_logs`
21. **SMS Management**: `sms_schedule`
22. **POS Register**: `pos_register`

### Default Data Included:
- **Admin User**: 
  - Username: `admin`
  - Password: `password` (must be changed)
  - Email: `<EMAIL>`

- **Default Store**: Modern POS Store
- **Currencies**: Saudi Riyal and US Dollar
- **Units**: Piece, Kilogram, Liter, Meter
- **Categories**: General, Electronics, Clothing, Food & Beverages
- **Tax Rates**: VAT 15% and No Tax
- **Payment Methods**: Cash, Credit Card, Debit Card, Bank Transfer
- **Expense Categories**: Office Supplies, Utilities, Marketing, Transportation
- **Income Sources**: Product Sales, Service Income, Other Income

## Import Methods

### Method 1: Using phpMyAdmin
1. Open phpMyAdmin in your browser
2. Create a new database named `modernpos`
3. Select the database
4. Click on "Import" tab
5. Choose the `modernpos_database_complete.sql` file
6. Click "Go" to execute

### Method 2: Using Command Line
```bash
mysql -u root -p -e "CREATE DATABASE modernpos;"
mysql -u root -p modernpos < modernpos_database_complete.sql
```

### Method 3: Using MySQL Workbench
1. Open MySQL Workbench
2. Connect to your server
3. Go to Server > Data Import
4. Select "Import from Self-Contained File"
5. Choose the `modernpos_database_complete.sql` file
6. Select target database or create new one
7. Click "Start Import"

## Configuration Setup
After importing the database, make sure to update the `config.php` file:

```php
$sql_details = array(
    'host' => 'localhost',
    'db' => 'modernpos',
    'user' => 'root',
    'pass' => 'your_database_password',
    'port' => '3306'
);
```

## Post-Import Steps

### 1. Change Admin Password
- Login with user `admin`
- Go to user settings
- Change the default password

### 2. Update Store Information
- Go to store settings
- Update store name, address, and contact information
- Upload store logo

### 3. Setup Default Currency
- Go to currency settings
- Set the default currency for your store

### 4. Add Products
- Start adding your products
- Set appropriate categories and units
- Add purchase and selling prices

### 5. Configure Payment Methods
- Enable required payment methods
- Add additional payment methods if needed

## Important Notes

### Security:
- Change admin password immediately
- Create regular database backups
- Ensure `config.php` file is protected

### Performance:
- Ensure indexes exist on large tables
- Monitor database size and performance
- Clean old records regularly

### Updates:
- Keep backup before any update
- Test updates in staging environment first

## Troubleshooting

### Import Error:
- Ensure MySQL is running
- Check user permissions
- Verify sufficient disk space

### Connection Error:
- Check database settings in `config.php`
- Verify database name is correct
- Check username and password

## Support
If you encounter any issues, make sure to:
1. Read error messages carefully
2. Check error logs
3. Review database settings

---

**File Created:** 2025-01-01  
**Database Version:** ModernPOS Complete Schema v1.0
