-- الجداول المفقودة لنظام ModernPOS
-- استورد هذا الملف لإضافة الجداول المفقودة

USE `kidzrcle_MBEAAAT`;

-- --------------------------------------------------------
-- هيكل الجدول `languages` - اللغات
-- --------------------------------------------------------

DROP TABLE IF EXISTS `languages`;
CREATE TABLE `languages` (
  `language_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(32) NOT NULL,
  `code` varchar(5) NOT NULL,
  `locale` varchar(255) NOT NULL,
  `image` varchar(64) NOT NULL,
  `directory` varchar(3) NOT NULL,
  `sort_order` int(3) NOT NULL DEFAULT 0,
  `status` tinyint(1) NOT NULL,
  PRIMARY KEY (`language_id`),
  KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- إدراج اللغات الافتراضية
INSERT INTO `languages` (`language_id`, `name`, `code`, `locale`, `image`, `directory`, `sort_order`, `status`) VALUES
(1, 'English', 'en-gb', 'en_US.UTF-8,en_US,en-gb,en_GB,english', 'gb.png', 'ltr', 1, 1),
(2, 'العربية', 'ar', 'ar_SA.UTF-8,ar_SA,ar,arabic', 'sa.png', 'rtl', 2, 1);

-- --------------------------------------------------------
-- هيكل الجدول `language_text` - نصوص الترجمة
-- --------------------------------------------------------

DROP TABLE IF EXISTS `language_text`;
CREATE TABLE `language_text` (
  `text_id` int(11) NOT NULL AUTO_INCREMENT,
  `language_id` int(11) NOT NULL,
  `route` varchar(64) NOT NULL,
  `key` varchar(64) NOT NULL,
  `value` text NOT NULL,
  PRIMARY KEY (`text_id`),
  KEY `language_id` (`language_id`),
  KEY `route` (`route`),
  KEY `key` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- إدراج النصوص الأساسية
INSERT INTO `language_text` (`language_id`, `route`, `key`, `value`) VALUES
(1, 'common', 'text_login_title', 'Login - ModernPOS'),
(1, 'common', 'text_dashboard', 'Dashboard'),
(1, 'common', 'text_logout', 'Logout'),
(1, 'common', 'text_welcome', 'Welcome'),
(1, 'common', 'error_username_or_password', 'Invalid username or password'),
(1, 'common', 'error_login_attempts_exceeded', 'Too many login attempts'),
(1, 'common', 'text_pos', 'Point of Sale'),
(1, 'common', 'text_products', 'Products'),
(1, 'common', 'text_customers', 'Customers'),
(1, 'common', 'text_suppliers', 'Suppliers'),
(1, 'common', 'text_reports', 'Reports'),
(1, 'common', 'text_settings', 'Settings'),
(1, 'common', 'text_sales', 'Sales'),
(1, 'common', 'text_purchases', 'Purchases'),
(1, 'common', 'text_inventory', 'Inventory'),
(2, 'common', 'text_login_title', 'تسجيل الدخول - نظام نقاط البيع'),
(2, 'common', 'text_dashboard', 'لوحة التحكم'),
(2, 'common', 'text_logout', 'تسجيل الخروج'),
(2, 'common', 'text_welcome', 'مرحباً'),
(2, 'common', 'error_username_or_password', 'اسم المستخدم أو كلمة المرور غير صحيحة'),
(2, 'common', 'error_login_attempts_exceeded', 'تم تجاوز عدد محاولات تسجيل الدخول المسموحة'),
(2, 'common', 'text_pos', 'نقطة البيع'),
(2, 'common', 'text_products', 'المنتجات'),
(2, 'common', 'text_customers', 'العملاء'),
(2, 'common', 'text_suppliers', 'الموردين'),
(2, 'common', 'text_reports', 'التقارير'),
(2, 'common', 'text_settings', 'الإعدادات'),
(2, 'common', 'text_sales', 'المبيعات'),
(2, 'common', 'text_purchases', 'المشتريات'),
(2, 'common', 'text_inventory', 'المخزون');

-- --------------------------------------------------------
-- هيكل الجدول `sessions` - الجلسات
-- --------------------------------------------------------

DROP TABLE IF EXISTS `sessions`;
CREATE TABLE `sessions` (
  `session_id` varchar(32) NOT NULL,
  `data` text NOT NULL,
  `expire` datetime NOT NULL,
  PRIMARY KEY (`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `settings_general` - الإعدادات العامة
-- --------------------------------------------------------

DROP TABLE IF EXISTS `settings_general`;
CREATE TABLE `settings_general` (
  `setting_id` int(11) NOT NULL AUTO_INCREMENT,
  `group` varchar(32) NOT NULL,
  `key` varchar(64) NOT NULL,
  `value` text NOT NULL,
  `serialized` tinyint(1) NOT NULL,
  PRIMARY KEY (`setting_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- إدراج الإعدادات الافتراضية
INSERT INTO `settings_general` (`group`, `key`, `value`, `serialized`) VALUES
('config', 'config_name', 'ModernPOS', 0),
('config', 'config_title', 'Modern Point of Sale System', 0),
('config', 'config_meta_title', 'ModernPOS - Point of Sale', 0),
('config', 'config_meta_description', 'Modern Point of Sale System with Inventory Management', 0),
('config', 'config_template', 'default', 0),
('config', 'config_default_language_id', '1', 0),
('config', 'config_admin_language_id', '1', 0),
('config', 'config_currency', 'SAR', 0),
('config', 'config_currency_auto', '1', 0),
('config', 'config_length_class_id', '1', 0),
('config', 'config_weight_class_id', '1', 0),
('config', 'config_timezone', 'Asia/Riyadh', 0);

-- --------------------------------------------------------
-- هيكل الجدول `user_activity` - نشاط المستخدمين
-- --------------------------------------------------------

DROP TABLE IF EXISTS `user_activity`;
CREATE TABLE `user_activity` (
  `activity_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `key` varchar(64) NOT NULL,
  `data` text NOT NULL,
  `ip` varchar(40) NOT NULL,
  `date_added` datetime NOT NULL,
  PRIMARY KEY (`activity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `user_login` - سجل دخول المستخدمين
-- --------------------------------------------------------

DROP TABLE IF EXISTS `user_login`;
CREATE TABLE `user_login` (
  `user_login_id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(96) NOT NULL,
  `ip` varchar(40) NOT NULL,
  `total` int(4) NOT NULL,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL,
  PRIMARY KEY (`user_login_id`),
  KEY `email` (`email`),
  KEY `ip` (`ip`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- إضافة البيانات الأساسية للمستخدمين إذا لم تكن موجودة
-- --------------------------------------------------------

-- إدراج مجموعة المستخدمين الافتراضية إذا لم تكن موجودة
INSERT IGNORE INTO `user_groups` (`group_id`, `group_name`, `permissions`, `created_at`) VALUES
(1, 'Administrator', 'all', NOW()),
(2, 'Manager', 'limited', NOW()),
(3, 'Cashier', 'basic', NOW());

-- إدراج المستخدم الافتراضي إذا لم يكن موجوداً
INSERT IGNORE INTO `users` (`user_id`, `username`, `email`, `mobile`, `password`, `group_id`, `dob`, `user_image`, `status`, `created_at`) VALUES
(1, 'admin', '<EMAIL>', '1234567890', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1, '1990-01-01', NULL, 1, NOW());

-- إدراج المتجر الافتراضي إذا لم يكن موجوداً
INSERT IGNORE INTO `stores` (`store_id`, `name`, `code_name`, `mobile`, `email`, `country`, `vat_reg_no`, `zip_code`, `cashier_id`, `address`, `logo`, `favicon`, `sound_effect`, `status`, `sort_order`, `receipt_printer`, `remote_printing`, `auto_print`, `currency`, `created_at`) VALUES
(1, 'Modern POS Store', 'MAIN', '1234567890', '<EMAIL>', 'Saudi Arabia', '', '12345', 1, 'Main Street, City', NULL, NULL, NULL, 1, 1, NULL, NULL, 0, 'SAR', NOW());

-- ربط المستخدم بالمتجر إذا لم يكن مربوطاً
INSERT IGNORE INTO `user_to_store` (`user_id`, `store_id`) VALUES (1, 1);

-- إدراج العملة الافتراضية إذا لم تكن موجودة
INSERT IGNORE INTO `currency` (`currency_id`, `title`, `code`, `symbol_left`, `symbol_right`, `decimal_place`, `value`, `created_at`) VALUES
(1, 'Saudi Riyal', 'SAR', 'ر.س', '', 2, 1.00000000, NOW()),
(2, 'US Dollar', 'USD', '$', '', 2, 3.75000000, NOW());

-- ربط العملة بالمتجر إذا لم تكن مربوطة
INSERT IGNORE INTO `currency_to_store` (`currency_id`, `store_id`, `status`, `sort_order`) VALUES
(1, 1, 1, 1),
(2, 1, 1, 2);

-- إدراج الوحدات الافتراضية إذا لم تكن موجودة
INSERT IGNORE INTO `units` (`unit_id`, `unit_name`, `code_name`, `unit_details`, `created_at`) VALUES
(1, 'Piece', 'PCS', 'Individual pieces', NOW()),
(2, 'Kilogram', 'KG', 'Weight in kilograms', NOW()),
(3, 'Liter', 'L', 'Volume in liters', NOW()),
(4, 'Meter', 'M', 'Length in meters', NOW());

-- ربط الوحدات بالمتجر إذا لم تكن مربوطة
INSERT IGNORE INTO `unit_to_store` (`uunit_id`, `store_id`, `status`, `sort_order`) VALUES
(1, 1, 1, 1),
(2, 1, 1, 2),
(3, 1, 1, 3),
(4, 1, 1, 4);

-- إدراج الفئات الافتراضية إذا لم تكن موجودة
INSERT IGNORE INTO `categorys` (`category_id`, `category_name`, `category_slug`, `parent_id`, `category_details`, `category_image`, `created_at`) VALUES
(1, 'General', 'general', 0, 'General category for all products', NULL, NOW()),
(2, 'Electronics', 'electronics', 0, 'Electronic devices and accessories', NULL, NOW()),
(3, 'Clothing', 'clothing', 0, 'Clothes and fashion items', NULL, NOW()),
(4, 'Food & Beverages', 'food-beverages', 0, 'Food and drink items', NULL, NOW());

-- ربط الفئات بالمتجر إذا لم تكن مربوطة
INSERT IGNORE INTO `category_to_store` (`ccategory_id`, `store_id`, `status`, `sort_order`) VALUES
(1, 1, 1, 1),
(2, 1, 1, 2),
(3, 1, 1, 3),
(4, 1, 1, 4);

-- إدراج معدلات الضرائب الافتراضية إذا لم تكن موجودة
INSERT IGNORE INTO `taxrates` (`taxrate_id`, `taxrate_name`, `taxrate_percent`, `created_at`) VALUES
(1, 'VAT 15%', 15.0000, NOW()),
(2, 'No Tax', 0.0000, NOW());

-- ربط معدلات الضرائب بالمتجر إذا لم تكن مربوطة
INSERT IGNORE INTO `taxrate_to_store` (`taxrate_id`, `store_id`, `status`, `sort_order`) VALUES
(1, 1, 1, 1),
(2, 1, 1, 2);

-- إدراج طرق الدفع الافتراضية إذا لم تكن موجودة
INSERT IGNORE INTO `payment_methods` (`method_id`, `method_name`, `method_code`, `description`, `status`, `sort_order`, `created_at`) VALUES
(1, 'Cash', 'cash', 'Cash payment', 1, 1, NOW()),
(2, 'Credit Card', 'credit_card', 'Credit card payment', 1, 2, NOW()),
(3, 'Debit Card', 'debit_card', 'Debit card payment', 1, 3, NOW()),
(4, 'Bank Transfer', 'bank_transfer', 'Bank transfer payment', 1, 4, NOW());

-- إدراج فئات المصروفات الافتراضية إذا لم تكن موجودة
INSERT IGNORE INTO `expense_categorys` (`expense_category_id`, `expense_category_name`, `expense_category_details`, `created_at`) VALUES
(1, 'Office Supplies', 'Office supplies and stationery', NOW()),
(2, 'Utilities', 'Electricity, water, internet bills', NOW()),
(3, 'Marketing', 'Advertising and promotional expenses', NOW()),
(4, 'Transportation', 'Vehicle and transportation costs', NOW());

-- إدراج مصادر الدخل الافتراضية إذا لم تكن موجودة
INSERT IGNORE INTO `income_sources` (`income_source_id`, `income_source_name`, `income_source_details`, `created_at`) VALUES
(1, 'Product Sales', 'Revenue from product sales', NOW()),
(2, 'Service Income', 'Revenue from services', NOW()),
(3, 'Other Income', 'Miscellaneous income sources', NOW());
