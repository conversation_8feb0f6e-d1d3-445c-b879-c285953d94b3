<?php
// إعدادات قاعدة البيانات
$host = 'localhost';
$dbname = 'modernpos';
$username = 'root';
$password = '';
$port = '3306';

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "تم الاتصال بقاعدة البيانات بنجاح!\n";
    
    // الحصول على قائمة الجداول
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "عدد الجداول الموجودة: " . count($tables) . "\n";
    
    // بداية ملف SQL
    $sql_content = "-- ملف قاعدة البيانات الكامل لنظام ModernPOS\n";
    $sql_content .= "-- تم إنشاؤه في: " . date('Y-m-d H:i:s') . "\n";
    $sql_content .= "-- اسم قاعدة البيانات: $dbname\n\n";
    
    $sql_content .= "SET SQL_MODE = \"NO_AUTO_VALUE_ON_ZERO\";\n";
    $sql_content .= "START TRANSACTION;\n";
    $sql_content .= "SET time_zone = \"+00:00\";\n\n";
    
    $sql_content .= "-- إنشاء قاعدة البيانات\n";
    $sql_content .= "CREATE DATABASE IF NOT EXISTS `$dbname` DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;\n";
    $sql_content .= "USE `$dbname`;\n\n";
    
    // تصدير هيكل كل جدول
    foreach ($tables as $table) {
        echo "معالجة الجدول: $table\n";
        
        // الحصول على هيكل الجدول
        $stmt = $pdo->query("SHOW CREATE TABLE `$table`");
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $sql_content .= "-- --------------------------------------------------------\n";
        $sql_content .= "-- هيكل الجدول `$table`\n";
        $sql_content .= "-- --------------------------------------------------------\n\n";
        
        $sql_content .= "DROP TABLE IF EXISTS `$table`;\n";
        $sql_content .= $row['Create Table'] . ";\n\n";
        
        // تصدير البيانات
        $stmt = $pdo->query("SELECT * FROM `$table`");
        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($rows)) {
            $sql_content .= "-- تفريغ بيانات الجدول `$table`\n";
            
            // الحصول على أسماء الأعمدة
            $columns = array_keys($rows[0]);
            $columnsList = '`' . implode('`, `', $columns) . '`';
            
            $sql_content .= "INSERT INTO `$table` ($columnsList) VALUES\n";
            
            $values = array();
            foreach ($rows as $row) {
                $rowValues = array();
                foreach ($row as $value) {
                    if ($value === null) {
                        $rowValues[] = 'NULL';
                    } else {
                        $rowValues[] = "'" . addslashes($value) . "'";
                    }
                }
                $values[] = '(' . implode(', ', $rowValues) . ')';
            }
            
            $sql_content .= implode(",\n", $values) . ";\n\n";
        }
    }
    
    $sql_content .= "COMMIT;\n";
    
    // حفظ الملف
    $filename = 'modernpos_database_' . date('Y-m-d_H-i-s') . '.sql';
    file_put_contents($filename, $sql_content);
    
    echo "تم إنشاء ملف قاعدة البيانات بنجاح: $filename\n";
    echo "حجم الملف: " . number_format(filesize($filename) / 1024, 2) . " KB\n";
    
} catch (PDOException $e) {
    echo "خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "\n";
}
?>
