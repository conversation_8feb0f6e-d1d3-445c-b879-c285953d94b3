# تعليمات استيراد قاعدة البيانات لنظام ModernPOS

## نظرة عامة
تم إنشاء ملف `modernpos_database_complete.sql` الذي يحتوي على:
- هيكل قاعدة البيانات الكامل (جميع الجداول)
- البيانات الأساسية المطلوبة لتشغيل النظام
- إعدادات افتراضية للمتجر والمستخدمين

## محتويات قاعدة البيانات

### الجداول الرئيسية:
1. **إدارة المتاجر**: `stores`, `settings`
2. **إدارة المستخدمين**: `users`, `user_groups`, `user_to_store`
3. **إدارة العملاء**: `customers`, `customer_transactions`
4. **إدارة الموردين**: `suppliers`
5. **إدارة المنتجات**: `products`, `product_to_store`, `categorys`, `brands`, `units`
6. **إدارة المبيعات**: `selling_info`, `selling_item`, `selling_price`
7. **إدارة المشتريات**: `purchase_info`, `purchase_item`, `purchase_price`
8. **إدارة المدفوعات**: `payments`, `payment_methods`
9. **إدارة المرتجعات**: `returns`, `return_items`
10. **إدارة العروض**: `quotation_info`, `quotation_item`, `quotation_price`
11. **إدارة الطلبات المعلقة**: `holding_info`, `holding_item`, `holding_price`
12. **إدارة المصروفات**: `expenses`, `expense_categorys`
13. **إدارة البنوك**: `bank_accounts`, `bank_transaction_info`
14. **إدارة القروض**: `loans`
15. **إدارة الأقساط**: `installment_orders`, `installment_payments`
16. **إدارة التحويلات**: `transfer_items`
17. **إدارة الضرائب**: `taxrates`, `taxrate_to_store`
18. **إدارة العملات**: `currency`, `currency_to_store`
19. **إدارة بطاقات الهدايا**: `giftcards`
20. **إدارة السجلات**: `sell_logs`, `purchase_logs`
21. **إدارة الرسائل النصية**: `sms_schedule`
22. **إدارة نقاط البيع**: `pos_register`

### البيانات الافتراضية المدرجة:
- **مستخدم إداري**: 
  - اسم المستخدم: `admin`
  - كلمة المرور: `password` (يجب تغييرها)
  - البريد الإلكتروني: `<EMAIL>`

- **متجر افتراضي**: Modern POS Store
- **عملات**: الريال السعودي والدولار الأمريكي
- **وحدات القياس**: قطعة، كيلوغرام، لتر، متر
- **فئات المنتجات**: عام، إلكترونيات، ملابس، طعام ومشروبات
- **معدلات الضرائب**: ضريبة القيمة المضافة 15% وبدون ضريبة
- **طرق الدفع**: نقدي، بطاقة ائتمان، بطاقة خصم، تحويل بنكي
- **فئات المصروفات**: لوازم مكتبية، مرافق، تسويق، نقل
- **مصادر الدخل**: مبيعات المنتجات، دخل الخدمات، دخل آخر

## طريقة الاستيراد

### الطريقة الأولى: استخدام phpMyAdmin
1. افتح phpMyAdmin في المتصفح
2. قم بإنشاء قاعدة بيانات جديدة باسم `modernpos`
3. اختر قاعدة البيانات
4. انقر على تبويب "Import" أو "استيراد"
5. اختر ملف `modernpos_database_complete.sql`
6. انقر على "Go" أو "تنفيذ"

### الطريقة الثانية: استخدام سطر الأوامر
```bash
mysql -u root -p -e "CREATE DATABASE modernpos;"
mysql -u root -p modernpos < modernpos_database_complete.sql
```

### الطريقة الثالثة: استخدام MySQL Workbench
1. افتح MySQL Workbench
2. اتصل بالخادم
3. اذهب إلى Server > Data Import
4. اختر "Import from Self-Contained File"
5. حدد ملف `modernpos_database_complete.sql`
6. اختر قاعدة البيانات الهدف أو أنشئ واحدة جديدة
7. انقر على "Start Import"

## إعداد ملف التكوين
بعد استيراد قاعدة البيانات، تأكد من تحديث ملف `config.php`:

```php
$sql_details = array(
    'host' => 'localhost',
    'db' => 'modernpos',
    'user' => 'root',
    'pass' => 'كلمة_مرور_قاعدة_البيانات',
    'port' => '3306'
);
```

## خطوات ما بعد الاستيراد

### 1. تغيير كلمة مرور المدير
- سجل دخول بالمستخدم `admin`
- اذهب إلى إعدادات المستخدم
- غيّر كلمة المرور الافتراضية

### 2. تحديث معلومات المتجر
- اذهب إلى إعدادات المتجر
- حدّث اسم المتجر والعنوان ومعلومات الاتصال
- ارفع شعار المتجر

### 3. إعداد العملة الافتراضية
- اذهب إلى إعدادات العملات
- حدد العملة الافتراضية للمتجر

### 4. إضافة المنتجات
- ابدأ بإضافة المنتجات الخاصة بك
- حدد الفئات والوحدات المناسبة
- أضف أسعار الشراء والبيع

### 5. إعداد طرق الدفع
- فعّل طرق الدفع المطلوبة
- أضف طرق دفع إضافية إذا لزم الأمر

## ملاحظات مهمة

### الأمان:
- غيّر كلمة مرور المدير فوراً
- أنشئ نسخة احتياطية من قاعدة البيانات بانتظام
- تأكد من أن ملف `config.php` محمي

### الأداء:
- تأكد من وجود فهارس على الجداول الكبيرة
- راقب حجم قاعدة البيانات وأداءها
- نظف السجلات القديمة بانتظام

### التحديثات:
- احتفظ بنسخة احتياطية قبل أي تحديث
- اختبر التحديثات في بيئة تجريبية أولاً

## استكشاف الأخطاء

### خطأ في الاستيراد:
- تأكد من أن MySQL يعمل
- تحقق من صلاحيات المستخدم
- تأكد من وجود مساحة كافية

### خطأ في الاتصال:
- تحقق من إعدادات قاعدة البيانات في `config.php`
- تأكد من أن اسم قاعدة البيانات صحيح
- تحقق من اسم المستخدم وكلمة المرور

## الدعم
إذا واجهت أي مشاكل، تأكد من:
1. قراءة رسائل الخطأ بعناية
2. التحقق من سجلات الأخطاء
3. مراجعة إعدادات قاعدة البيانات

---

**تم إنشاء هذا الملف في:** 2025-01-01  
**إصدار قاعدة البيانات:** ModernPOS Complete Schema v1.0
