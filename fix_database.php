<?php
/**
 * سكريبت إصلاح قاعدة البيانات تلقائياً
 * يقوم بإنشاء الجداول المفقودة وإضافة البيانات الأساسية
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 إصلاح قاعدة البيانات تلقائياً</h1>";

try {
    // تحميل إعدادات قاعدة البيانات
    require_once 'config.php';
    
    $pdo = new PDO(
        "mysql:host={$sql_details['host']};port={$sql_details['port']};dbname={$sql_details['db']};charset=utf8",
        $sql_details['user'],
        $sql_details['pass']
    );
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // 1. إنشاء جدول languages
    echo "<h2>1. إنشاء جدول languages</h2>";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `languages` (
            `language_id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(32) NOT NULL,
            `code` varchar(5) NOT NULL,
            `locale` varchar(255) NOT NULL,
            `image` varchar(64) NOT NULL,
            `directory` varchar(3) NOT NULL,
            `sort_order` int(3) NOT NULL DEFAULT 0,
            `status` tinyint(1) NOT NULL,
            PRIMARY KEY (`language_id`),
            KEY `name` (`name`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8
    ");
    
    // إدراج اللغات الافتراضية
    $pdo->exec("
        INSERT IGNORE INTO `languages` VALUES
        (1, 'English', 'en-gb', 'en_US.UTF-8,en_US,en-gb,en_GB,english', 'gb.png', 'ltr', 1, 1),
        (2, 'العربية', 'ar', 'ar_SA.UTF-8,ar_SA,ar,arabic', 'sa.png', 'rtl', 2, 1)
    ");
    echo "<p>✅ جدول languages تم إنشاؤه</p>";
    
    // 2. إنشاء جدول language_text
    echo "<h2>2. إنشاء جدول language_text</h2>";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `language_text` (
            `text_id` int(11) NOT NULL AUTO_INCREMENT,
            `language_id` int(11) NOT NULL,
            `route` varchar(64) NOT NULL,
            `key` varchar(64) NOT NULL,
            `value` text NOT NULL,
            PRIMARY KEY (`text_id`),
            KEY `language_id` (`language_id`),
            KEY `route` (`route`),
            KEY `key` (`key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8
    ");
    echo "<p>✅ جدول language_text تم إنشاؤه</p>";
    
    // 3. إنشاء جدول sessions
    echo "<h2>3. إنشاء جدول sessions</h2>";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `sessions` (
            `session_id` varchar(32) NOT NULL,
            `data` text NOT NULL,
            `expire` datetime NOT NULL,
            PRIMARY KEY (`session_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8
    ");
    echo "<p>✅ جدول sessions تم إنشاؤه</p>";
    
    // 4. إنشاء جدول settings_general
    echo "<h2>4. إنشاء جدول settings_general</h2>";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `settings_general` (
            `setting_id` int(11) NOT NULL AUTO_INCREMENT,
            `group` varchar(32) NOT NULL,
            `key` varchar(64) NOT NULL,
            `value` text NOT NULL,
            `serialized` tinyint(1) NOT NULL,
            PRIMARY KEY (`setting_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8
    ");
    echo "<p>✅ جدول settings_general تم إنشاؤه</p>";
    
    // 5. التأكد من وجود جدول user_groups
    echo "<h2>5. التأكد من جدول user_groups</h2>";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `user_groups` (
            `group_id` int(11) NOT NULL AUTO_INCREMENT,
            `group_name` varchar(100) NOT NULL,
            `permissions` text DEFAULT NULL,
            `created_at` datetime DEFAULT NULL,
            PRIMARY KEY (`group_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8
    ");
    
    // إدراج مجموعات المستخدمين
    $pdo->exec("
        INSERT IGNORE INTO `user_groups` VALUES
        (1, 'Administrator', 'all', NOW()),
        (2, 'Manager', 'limited', NOW()),
        (3, 'Cashier', 'basic', NOW())
    ");
    echo "<p>✅ جدول user_groups تم التحقق منه</p>";
    
    // 6. التأكد من وجود جدول users
    echo "<h2>6. التأكد من جدول users</h2>";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `users` (
            `user_id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(100) NOT NULL,
            `email` varchar(100) NOT NULL,
            `mobile` varchar(20) DEFAULT NULL,
            `password` varchar(255) NOT NULL,
            `group_id` int(11) NOT NULL,
            `dob` date DEFAULT NULL,
            `user_image` varchar(255) DEFAULT NULL,
            `status` tinyint(1) DEFAULT 1,
            `created_at` datetime DEFAULT NULL,
            PRIMARY KEY (`user_id`),
            UNIQUE KEY `username` (`username`),
            UNIQUE KEY `email` (`email`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8
    ");
    
    // إدراج المستخدم الافتراضي
    $pdo->exec("
        INSERT IGNORE INTO `users` VALUES
        (1, 'admin', '<EMAIL>', '1234567890', '$2y$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1, '1990-01-01', NULL, 1, NOW())
    ");
    echo "<p>✅ جدول users تم التحقق منه</p>";
    
    // 7. التأكد من وجود جدول stores
    echo "<h2>7. التأكد من جدول stores</h2>";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `stores` (
            `store_id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `code_name` varchar(100) DEFAULT NULL,
            `mobile` varchar(20) DEFAULT NULL,
            `email` varchar(100) DEFAULT NULL,
            `country` varchar(100) DEFAULT NULL,
            `vat_reg_no` varchar(50) DEFAULT NULL,
            `zip_code` varchar(20) DEFAULT NULL,
            `cashier_id` int(11) DEFAULT NULL,
            `address` text DEFAULT NULL,
            `logo` varchar(255) DEFAULT NULL,
            `favicon` varchar(255) DEFAULT NULL,
            `sound_effect` varchar(255) DEFAULT NULL,
            `status` tinyint(1) DEFAULT 1,
            `sort_order` int(11) DEFAULT 0,
            `receipt_printer` varchar(255) DEFAULT NULL,
            `remote_printing` varchar(255) DEFAULT NULL,
            `auto_print` tinyint(1) DEFAULT 0,
            `currency` varchar(10) DEFAULT NULL,
            `created_at` datetime DEFAULT NULL,
            PRIMARY KEY (`store_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8
    ");
    
    // إدراج المتجر الافتراضي
    $pdo->exec("
        INSERT IGNORE INTO `stores` VALUES
        (1, 'Modern POS Store', 'MAIN', '1234567890', '<EMAIL>', 'Saudi Arabia', '', '12345', 1, 'Main Street, City', NULL, NULL, NULL, 1, 1, NULL, NULL, 0, 'SAR', NOW())
    ");
    echo "<p>✅ جدول stores تم التحقق منه</p>";
    
    // 8. التأكد من وجود جدول user_to_store
    echo "<h2>8. التأكد من جدول user_to_store</h2>";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `user_to_store` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) NOT NULL,
            `store_id` int(11) NOT NULL,
            PRIMARY KEY (`id`),
            KEY `user_id` (`user_id`),
            KEY `store_id` (`store_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8
    ");
    
    // ربط المستخدم بالمتجر
    $pdo->exec("INSERT IGNORE INTO `user_to_store` VALUES (1, 1, 1)");
    echo "<p>✅ جدول user_to_store تم التحقق منه</p>";
    
    // 9. فحص النتائج النهائية
    echo "<h2>9. فحص النتائج النهائية</h2>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE status = 1");
    $userCount = $stmt->fetchColumn();
    echo "<p>عدد المستخدمين النشطين: <strong>$userCount</strong></p>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM languages WHERE status = 1");
    $langCount = $stmt->fetchColumn();
    echo "<p>عدد اللغات النشطة: <strong>$langCount</strong></p>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM stores WHERE status = 1");
    $storeCount = $stmt->fetchColumn();
    echo "<p>عدد المتاجر النشطة: <strong>$storeCount</strong></p>";
    
    if ($userCount > 0 && $langCount > 0 && $storeCount > 0) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>🎉 تم إصلاح قاعدة البيانات بنجاح!</h3>";
        echo "<p>يمكنك الآن محاولة الوصول للنظام:</p>";
        echo "<ul>";
        echo "<li><a href='index.php'>الصفحة الرئيسية</a></li>";
        echo "<li><a href='index_simple.php'>صفحة تسجيل الدخول المبسطة</a></li>";
        echo "<li><a href='test_simple.php'>اختبار النظام</a></li>";
        echo "</ul>";
        echo "<p><strong>بيانات الدخول:</strong></p>";
        echo "<ul>";
        echo "<li>اسم المستخدم: <code>admin</code></li>";
        echo "<li>كلمة المرور: <code>password</code></li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>⚠️ لا تزال هناك مشاكل</h3>";
        echo "<p>يرجى مراجعة الأخطاء أعلاه وإعادة المحاولة.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في إصلاح قاعدة البيانات</h3>";
    echo "<p><strong>الرسالة:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>تاريخ الإصلاح: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body { font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; background: #f5f5f5; }
h1, h2, h3 { color: #333; }
p { line-height: 1.6; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
