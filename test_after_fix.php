<?php
/**
 * اختبار النظام بعد إصلاح مشكلة أعمدة قاعدة البيانات
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 اختبار النظام بعد الإصلاح</h1>";

// اختبار 1: تحميل config.php
echo "<h2>1. اختبار config.php</h2>";
try {
    require_once 'config.php';
    echo "<p>✅ تم تحميل config.php بنجاح</p>";
    
    if (defined('INSTALLED') && INSTALLED) {
        echo "<p>✅ النظام مُثبت (INSTALLED = true)</p>";
    } else {
        echo "<p>❌ النظام غير مُثبت</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ خطأ في config.php: " . $e->getMessage() . "</p>";
}

// اختبار 2: الاتصال بقاعدة البيانات
echo "<h2>2. اختبار قاعدة البيانات</h2>";
try {
    $pdo = new PDO(
        "mysql:host={$sql_details['host']};port={$sql_details['port']};dbname={$sql_details['db']};charset=utf8",
        $sql_details['user'],
        $sql_details['pass']
    );
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // فحص جدول المستخدمين
    $stmt = $pdo->query("SELECT user_id, username, email, status FROM users WHERE status = 1");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>المستخدمين النشطين:</h3>";
    if (empty($users)) {
        echo "<p>❌ لا يوجد مستخدمين نشطين</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f2f2f2;'><th>ID</th><th>اسم المستخدم</th><th>البريد الإلكتروني</th><th>الحالة</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>{$user['user_id']}</td>";
            echo "<td>{$user['username']}</td>";
            echo "<td>{$user['email']}</td>";
            echo "<td>" . ($user['status'] ? 'نشط' : 'غير نشط') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

// اختبار 3: تحميل _init.php
echo "<h2>3. اختبار تحميل _init.php</h2>";
try {
    // تشغيل في buffer منفصل لتجنب المشاكل
    ob_start();
    
    // تعطيل exit و die مؤقتاً
    $old_error_handler = set_error_handler(function($severity, $message, $file, $line) {
        throw new ErrorException($message, 0, $severity, $file, $line);
    });
    
    include '_init.php';
    
    restore_error_handler();
    $output = ob_get_clean();
    
    echo "<p>✅ تم تحميل _init.php بنجاح!</p>";
    
    if (!empty($output)) {
        echo "<h4>مخرجات _init.php:</h4>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; max-height: 150px; overflow-y: auto;'>";
        echo htmlspecialchars($output);
        echo "</pre>";
    }
    
    // فحص المتغيرات المهمة
    if (isset($db)) {
        echo "<p>✅ متغير قاعدة البيانات \$db متوفر</p>";
    } else {
        echo "<p>❌ متغير قاعدة البيانات \$db غير متوفر</p>";
    }
    
    if (isset($user)) {
        echo "<p>✅ متغير المستخدم \$user متوفر</p>";
    } else {
        echo "<p>❌ متغير المستخدم \$user غير متوفر</p>";
    }
    
    if (isset($language)) {
        echo "<p>✅ متغير اللغة \$language متوفر</p>";
    } else {
        echo "<p>❌ متغير اللغة \$language غير متوفر</p>";
    }
    
} catch (Exception $e) {
    ob_end_clean();
    echo "<p>❌ خطأ في تحميل _init.php:</p>";
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>الرسالة:</strong> " . $e->getMessage() . "<br>";
    echo "<strong>الملف:</strong> " . $e->getFile() . "<br>";
    echo "<strong>السطر:</strong> " . $e->getLine() . "<br>";
    echo "</div>";
}

// اختبار 4: محاولة الوصول للصفحة الرئيسية
echo "<h2>4. اختبار الوصول للنظام</h2>";

if (isset($db) && isset($user)) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🎉 النظام جاهز للاستخدام!</h3>";
    echo "<p>جميع المكونات الأساسية تعمل بشكل صحيح.</p>";
    echo "<ul>";
    echo "<li><a href='index.php' target='_blank'>الصفحة الرئيسية</a></li>";
    echo "<li><a href='index_simple.php' target='_blank'>صفحة تسجيل الدخول المبسطة</a></li>";
    echo "<li><a href='admin/dashboard.php' target='_blank'>لوحة التحكم</a></li>";
    echo "</ul>";
    echo "<h4>بيانات تسجيل الدخول:</h4>";
    echo "<ul>";
    echo "<li><strong>اسم المستخدم:</strong> admin</li>";
    echo "<li><strong>كلمة المرور:</strong> password</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>⚠️ لا تزال هناك مشاكل</h3>";
    echo "<p>بعض المكونات لا تعمل بشكل صحيح. راجع الأخطاء أعلاه.</p>";
    echo "</div>";
}

// اختبار 5: فحص الملفات المهمة
echo "<h2>5. فحص الملفات المهمة</h2>";
$important_files = [
    'index.php' => 'الصفحة الرئيسية',
    'admin/dashboard.php' => 'لوحة التحكم',
    'admin/login.php' => 'صفحة تسجيل الدخول',
    '_inc/lib/database.php' => 'مكتبة قاعدة البيانات',
    '_inc/lib/user.php' => 'مكتبة المستخدم',
    'functions.php' => 'الدوال العامة'
];

echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
echo "<tr style='background: #f2f2f2;'><th>الملف</th><th>الوصف</th><th>الحالة</th></tr>";

foreach ($important_files as $file => $description) {
    $status = file_exists($file) ? '✅ موجود' : '❌ مفقود';
    $color = file_exists($file) ? '#d4edda' : '#f8d7da';
    
    echo "<tr style='background: $color;'>";
    echo "<td>$file</td>";
    echo "<td>$description</td>";
    echo "<td>$status</td>";
    echo "</tr>";
}

echo "</table>";

echo "<hr>";
echo "<h2>الخلاصة:</h2>";
echo "<p>إذا كانت جميع الاختبارات ناجحة، يمكنك الآن استخدام النظام بشكل طبيعي.</p>";
echo "<p>إذا كانت هناك مشاكل، راجع الأخطاء المذكورة أعلاه.</p>";

echo "<hr>";
echo "<p><small>تاريخ الاختبار: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}

h1, h2, h3 {
    color: #333;
}

p, li {
    line-height: 1.6;
}

table {
    margin: 10px 0;
}

th, td {
    padding: 8px;
    text-align: left;
}

th {
    background-color: #f2f2f2;
}

pre {
    white-space: pre-wrap;
    word-wrap: break-word;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
