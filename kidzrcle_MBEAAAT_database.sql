-- ملف قاعدة البيانات الكامل لنظام ModernPOS
-- تم إنشاؤه في: 2025-01-01
-- اسم قاعدة البيانات: kidzrcle_MBEAAAT
-- يحتوي على جميع الجداول والبيانات الأساسية

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS `kidzrcle_MBEAAAT` DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;
USE `kidzrcle_MBEAAAT`;

-- --------------------------------------------------------
-- هيك<PERSON> الجدول `stores` - المتاجر
-- --------------------------------------------------------

DROP TABLE IF EXISTS `stores`;
CREATE TABLE `stores` (
  `store_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `code_name` varchar(100) DEFAULT NULL,
  `mobile` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `country` varchar(100) DEFAULT NULL,
  `vat_reg_no` varchar(50) DEFAULT NULL,
  `zip_code` varchar(20) DEFAULT NULL,
  `cashier_id` int(11) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `logo` varchar(255) DEFAULT NULL,
  `favicon` varchar(255) DEFAULT NULL,
  `sound_effect` varchar(255) DEFAULT NULL,
  `status` tinyint(1) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  `receipt_printer` varchar(255) DEFAULT NULL,
  `remote_printing` varchar(255) DEFAULT NULL,
  `auto_print` tinyint(1) DEFAULT 0,
  `currency` varchar(10) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `users` - المستخدمين
-- --------------------------------------------------------

DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `user_id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `mobile` varchar(20) DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `group_id` int(11) NOT NULL,
  `dob` date DEFAULT NULL,
  `user_image` varchar(255) DEFAULT NULL,
  `status` tinyint(1) DEFAULT 1,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `user_groups` - مجموعات المستخدمين
-- --------------------------------------------------------

DROP TABLE IF EXISTS `user_groups`;
CREATE TABLE `user_groups` (
  `group_id` int(11) NOT NULL AUTO_INCREMENT,
  `group_name` varchar(100) NOT NULL,
  `permissions` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `user_to_store` - ربط المستخدمين بالمتاجر
-- --------------------------------------------------------

DROP TABLE IF EXISTS `user_to_store`;
CREATE TABLE `user_to_store` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `customers` - العملاء
-- --------------------------------------------------------

DROP TABLE IF EXISTS `customers`;
CREATE TABLE `customers` (
  `customer_id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_name` varchar(255) NOT NULL,
  `dob` date DEFAULT NULL,
  `customer_email` varchar(100) DEFAULT NULL,
  `customer_mobile` varchar(20) DEFAULT NULL,
  `customer_sex` enum('male','female') DEFAULT NULL,
  `customer_age` int(11) DEFAULT NULL,
  `gtin` varchar(50) DEFAULT NULL,
  `customer_address` text DEFAULT NULL,
  `customer_city` varchar(100) DEFAULT NULL,
  `customer_state` varchar(100) DEFAULT NULL,
  `customer_country` varchar(100) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`customer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `suppliers` - الموردين
-- --------------------------------------------------------

DROP TABLE IF EXISTS `suppliers`;
CREATE TABLE `suppliers` (
  `supplier_id` int(11) NOT NULL AUTO_INCREMENT,
  `supplier_name` varchar(255) NOT NULL,
  `supplier_email` varchar(100) DEFAULT NULL,
  `supplier_mobile` varchar(20) DEFAULT NULL,
  `supplier_address` text DEFAULT NULL,
  `supplier_city` varchar(100) DEFAULT NULL,
  `supplier_state` varchar(100) DEFAULT NULL,
  `supplier_country` varchar(100) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`supplier_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `categorys` - الفئات
-- --------------------------------------------------------

DROP TABLE IF EXISTS `categorys`;
CREATE TABLE `categorys` (
  `category_id` int(11) NOT NULL AUTO_INCREMENT,
  `category_name` varchar(255) NOT NULL,
  `category_slug` varchar(255) DEFAULT NULL,
  `parent_id` int(11) DEFAULT 0,
  `category_details` text DEFAULT NULL,
  `category_image` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `category_to_store` - ربط الفئات بالمتاجر
-- --------------------------------------------------------

DROP TABLE IF EXISTS `category_to_store`;
CREATE TABLE `category_to_store` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ccategory_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `status` tinyint(1) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `ccategory_id` (`ccategory_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `brands` - العلامات التجارية
-- --------------------------------------------------------

DROP TABLE IF EXISTS `brands`;
CREATE TABLE `brands` (
  `brand_id` int(11) NOT NULL AUTO_INCREMENT,
  `brand_name` varchar(255) NOT NULL,
  `code_name` varchar(100) DEFAULT NULL,
  `brand_details` text DEFAULT NULL,
  `brand_image` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`brand_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `brand_to_store` - ربط العلامات التجارية بالمتاجر
-- --------------------------------------------------------

DROP TABLE IF EXISTS `brand_to_store`;
CREATE TABLE `brand_to_store` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `brand_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `status` tinyint(1) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `brand_id` (`brand_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `units` - الوحدات
-- --------------------------------------------------------

DROP TABLE IF EXISTS `units`;
CREATE TABLE `units` (
  `unit_id` int(11) NOT NULL AUTO_INCREMENT,
  `unit_name` varchar(100) NOT NULL,
  `code_name` varchar(50) DEFAULT NULL,
  `unit_details` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`unit_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `unit_to_store` - ربط الوحدات بالمتاجر
-- --------------------------------------------------------

DROP TABLE IF EXISTS `unit_to_store`;
CREATE TABLE `unit_to_store` (
  `unit2s_id` int(11) NOT NULL AUTO_INCREMENT,
  `uunit_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `status` tinyint(1) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  PRIMARY KEY (`unit2s_id`),
  KEY `uunit_id` (`uunit_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `currency` - العملات
-- --------------------------------------------------------

DROP TABLE IF EXISTS `currency`;
CREATE TABLE `currency` (
  `currency_id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL,
  `code` varchar(10) NOT NULL,
  `symbol_left` varchar(10) DEFAULT NULL,
  `symbol_right` varchar(10) DEFAULT NULL,
  `decimal_place` int(11) DEFAULT 2,
  `value` decimal(15,8) DEFAULT 1.00000000,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`currency_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `currency_to_store` - ربط العملات بالمتاجر
-- --------------------------------------------------------

DROP TABLE IF EXISTS `currency_to_store`;
CREATE TABLE `currency_to_store` (
  `ca2s_id` int(11) NOT NULL AUTO_INCREMENT,
  `currency_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `status` tinyint(1) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  PRIMARY KEY (`ca2s_id`),
  KEY `currency_id` (`currency_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `products` - المنتجات
-- --------------------------------------------------------

DROP TABLE IF EXISTS `products`;
CREATE TABLE `products` (
  `product_id` int(11) NOT NULL AUTO_INCREMENT,
  `p_type` varchar(50) DEFAULT 'general',
  `p_name` varchar(255) NOT NULL,
  `p_code` varchar(100) DEFAULT NULL,
  `hsn_code` varchar(50) DEFAULT NULL,
  `barcode_symbology` varchar(50) DEFAULT 'CODE128',
  `category_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `p_image` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`product_id`),
  KEY `category_id` (`category_id`),
  KEY `unit_id` (`unit_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
