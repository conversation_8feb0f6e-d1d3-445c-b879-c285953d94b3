<?php
/**
 * الاختبار النهائي للنظام
 * يجب أن يعمل بدون أي أخطاء الآن
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🎯 الاختبار النهائي لنظام ModernPOS</h1>";

// اختبار 1: تحميل _init.php
echo "<h2>1. اختبار تحميل _init.php</h2>";
try {
    ob_start();
    include '_init.php';
    $output = ob_get_clean();
    
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>✅ تم تحميل _init.php بنجاح!</strong>";
    echo "</div>";
    
    if (!empty($output)) {
        echo "<h4>مخرجات _init.php:</h4>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; max-height: 100px; overflow-y: auto;'>";
        echo htmlspecialchars($output);
        echo "</pre>";
    }
    
    // فحص المتغيرات المهمة
    $components = [];
    
    if (isset($db)) {
        $components[] = "✅ قاعدة البيانات (\$db)";
    } else {
        $components[] = "❌ قاعدة البيانات (\$db)";
    }
    
    if (isset($user)) {
        $components[] = "✅ المستخدم (\$user)";
    } else {
        $components[] = "❌ المستخدم (\$user)";
    }
    
    if (isset($language)) {
        $components[] = "✅ اللغة (\$language)";
    } else {
        $components[] = "❌ اللغة (\$language)";
    }
    
    if (isset($session)) {
        $components[] = "✅ الجلسة (\$session)";
    } else {
        $components[] = "❌ الجلسة (\$session)";
    }
    
    echo "<h4>المكونات المحملة:</h4>";
    echo "<ul>";
    foreach ($components as $component) {
        echo "<li>$component</li>";
    }
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>❌ خطأ في تحميل _init.php:</strong><br>";
    echo "الرسالة: " . $e->getMessage() . "<br>";
    echo "الملف: " . $e->getFile() . "<br>";
    echo "السطر: " . $e->getLine() . "<br>";
    echo "</div>";
}

// اختبار 2: فحص وظائف النظام
echo "<h2>2. فحص وظائف النظام</h2>";

if (isset($db)) {
    try {
        // اختبار استعلام المستخدمين
        $stmt = $db->prepare("SELECT user_id, username, email FROM users WHERE status = 1 LIMIT 1");
        $stmt->execute();
        $user_test = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user_test) {
            echo "<p>✅ استعلام المستخدمين يعمل بشكل صحيح</p>";
            echo "<p>المستخدم الاختباري: {$user_test['username']} ({$user_test['email']})</p>";
        } else {
            echo "<p>❌ لا يوجد مستخدمين نشطين</p>";
        }
        
        // اختبار استعلام المتاجر
        $stmt = $db->prepare("SELECT store_id, name FROM stores WHERE status = 1 LIMIT 1");
        $stmt->execute();
        $store_test = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($store_test) {
            echo "<p>✅ استعلام المتاجر يعمل بشكل صحيح</p>";
            echo "<p>المتجر الاختباري: {$store_test['name']}</p>";
        } else {
            echo "<p>❌ لا يوجد متاجر نشطة</p>";
        }
        
        // اختبار استعلام اللغات
        $stmt = $db->prepare("SELECT language_id, name FROM languages WHERE status = 1 LIMIT 1");
        $stmt->execute();
        $lang_test = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($lang_test) {
            echo "<p>✅ استعلام اللغات يعمل بشكل صحيح</p>";
            echo "<p>اللغة الاختبارية: {$lang_test['name']}</p>";
        } else {
            echo "<p>❌ لا يوجد لغات نشطة</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ خطأ في اختبار الاستعلامات: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p>❌ قاعدة البيانات غير متوفرة للاختبار</p>";
}

// اختبار 3: فحص الدوال المهمة
echo "<h2>3. فحص الدوال المهمة</h2>";

$functions_to_test = [
    'db' => 'دالة قاعدة البيانات',
    'store_id' => 'دالة معرف المتجر',
    'user_id' => 'دالة معرف المستخدم',
    'trans' => 'دالة الترجمة',
    'date_time' => 'دالة التاريخ والوقت'
];

foreach ($functions_to_test as $function => $description) {
    if (function_exists($function)) {
        echo "<p>✅ $description ($function)</p>";
    } else {
        echo "<p>❌ $description ($function)</p>";
    }
}

// اختبار 4: النتيجة النهائية
echo "<h2>4. النتيجة النهائية</h2>";

$all_good = isset($db) && isset($user) && isset($language) && function_exists('db');

if ($all_good) {
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;'>";
    echo "<h3>🎉 النظام جاهز للاستخدام!</h3>";
    echo "<p>جميع المكونات الأساسية تعمل بشكل صحيح.</p>";
    echo "<hr>";
    echo "<h4>🚀 الخطوات التالية:</h4>";
    echo "<div style='text-align: left; margin: 20px 0;'>";
    echo "<p><strong>1. الوصول للنظام:</strong></p>";
    echo "<ul>";
    echo "<li><a href='index.php' target='_blank' style='color: #007bff; font-weight: bold;'>الصفحة الرئيسية</a></li>";
    echo "<li><a href='admin/dashboard.php' target='_blank' style='color: #007bff; font-weight: bold;'>لوحة التحكم</a></li>";
    echo "</ul>";
    
    echo "<p><strong>2. بيانات تسجيل الدخول:</strong></p>";
    echo "<ul>";
    echo "<li><strong>اسم المستخدم:</strong> <code style='background: #f8f9fa; padding: 2px 4px; border-radius: 3px;'>admin</code></li>";
    echo "<li><strong>كلمة المرور:</strong> <code style='background: #f8f9fa; padding: 2px 4px; border-radius: 3px;'>password</code></li>";
    echo "</ul>";
    
    echo "<p><strong>3. مهم جداً:</strong></p>";
    echo "<ul>";
    echo "<li>غيّر كلمة مرور المدير فوراً بعد تسجيل الدخول</li>";
    echo "<li>احذف ملفات الاختبار (test_*.php) لأسباب أمنية</li>";
    echo "<li>أنشئ نسخة احتياطية من قاعدة البيانات</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>⚠️ لا تزال هناك مشاكل</h3>";
    echo "<p>بعض المكونات الأساسية لا تعمل بشكل صحيح.</p>";
    echo "<p>راجع الأخطاء أعلاه وتأكد من:</p>";
    echo "<ul>";
    echo "<li>استيراد قاعدة البيانات بالكامل</li>";
    echo "<li>صحة إعدادات config.php</li>";
    echo "<li>وجود جميع الملفات المطلوبة</li>";
    echo "</ul>";
    echo "</div>";
}

// اختبار 5: تنظيف الملفات
echo "<h2>5. تنظيف الملفات</h2>";
echo "<p>بعد التأكد من عمل النظام، يُنصح بحذف ملفات الاختبار التالية لأسباب أمنية:</p>";
echo "<ul>";
echo "<li>test_simple.php</li>";
echo "<li>advanced_debug.php</li>";
echo "<li>debug_error.php</li>";
echo "<li>test_after_fix.php</li>";
echo "<li>final_test.php (هذا الملف)</li>";
echo "<li>fix_database.php</li>";
echo "<li>_init_simple.php</li>";
echo "</ul>";

echo "<hr>";
echo "<p><small>تاريخ الاختبار النهائي: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

p, li {
    margin: 8px 0;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

code {
    background: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}

pre {
    white-space: pre-wrap;
    word-wrap: break-word;
}

ul {
    padding-left: 20px;
}

hr {
    border: none;
    border-top: 1px solid #ddd;
    margin: 20px 0;
}
</style>
