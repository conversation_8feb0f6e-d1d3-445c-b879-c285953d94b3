<?php
/**
 * ملف تشخيص الأخطاء لنظام ModernPOS
 * استخدم هذا الملف لمعرفة سبب خطأ HTTP 500
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "<h1>تشخيص أخطاء نظام ModernPOS</h1>";
echo "<hr>";

// 1. فحص إصدار PHP
echo "<h2>1. معلومات PHP</h2>";
echo "<p><strong>إصدار PHP:</strong> " . phpversion() . "</p>";

if (version_compare(phpversion(), '7.0.0', '<')) {
    echo "<div style='color: red; background: #f8d7da; padding: 10px; border-radius: 5px;'>";
    echo "❌ <strong>خطأ:</strong> النظام يتطلب PHP 7.0 أو أحدث";
    echo "</div>";
} else {
    echo "<div style='color: green; background: #d4edda; padding: 10px; border-radius: 5px;'>";
    echo "✅ إصدار PHP مناسب";
    echo "</div>";
}

// 2. فحص الإضافات المطلوبة
echo "<h2>2. الإضافات المطلوبة</h2>";
$required_extensions = ['pdo', 'pdo_mysql', 'mysqli', 'json', 'mbstring', 'openssl', 'curl'];

foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<p>✅ <strong>$ext:</strong> متوفر</p>";
    } else {
        echo "<p>❌ <strong>$ext:</strong> غير متوفر</p>";
    }
}

// 3. فحص ملف config.php
echo "<h2>3. فحص ملف config.php</h2>";
if (file_exists('config.php')) {
    echo "<p>✅ ملف config.php موجود</p>";
    
    // محاولة تضمين الملف
    try {
        include_once 'config.php';
        echo "<p>✅ تم تحميل config.php بنجاح</p>";
        
        // فحص المتغيرات
        if (isset($sql_details)) {
            echo "<p>✅ متغير sql_details موجود</p>";
            echo "<ul>";
            echo "<li><strong>Host:</strong> " . $sql_details['host'] . "</li>";
            echo "<li><strong>Database:</strong> " . $sql_details['db'] . "</li>";
            echo "<li><strong>User:</strong> " . $sql_details['user'] . "</li>";
            echo "<li><strong>Port:</strong> " . $sql_details['port'] . "</li>";
            echo "</ul>";
        } else {
            echo "<p>❌ متغير sql_details غير موجود</p>";
        }
        
        if (defined('INSTALLED')) {
            echo "<p>✅ INSTALLED معرف: " . (INSTALLED ? 'true' : 'false') . "</p>";
        } else {
            echo "<p>❌ INSTALLED غير معرف</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ خطأ في تحميل config.php: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p>❌ ملف config.php غير موجود</p>";
}

// 4. اختبار الاتصال بقاعدة البيانات
echo "<h2>4. اختبار الاتصال بقاعدة البيانات</h2>";
if (isset($sql_details)) {
    try {
        $pdo = new PDO(
            "mysql:host={$sql_details['host']};port={$sql_details['port']};dbname={$sql_details['db']};charset=utf8",
            $sql_details['user'],
            $sql_details['pass']
        );
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<div style='color: green; background: #d4edda; padding: 10px; border-radius: 5px;'>";
        echo "✅ <strong>نجح الاتصال بقاعدة البيانات</strong>";
        echo "</div>";
        
        // فحص الجداول
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "<p><strong>عدد الجداول:</strong> " . count($tables) . "</p>";
        
        if (count($tables) == 0) {
            echo "<div style='color: orange; background: #fff3cd; padding: 10px; border-radius: 5px;'>";
            echo "⚠️ <strong>قاعدة البيانات فارغة - يجب استيراد ملف SQL</strong>";
            echo "</div>";
        }
        
    } catch (PDOException $e) {
        echo "<div style='color: red; background: #f8d7da; padding: 10px; border-radius: 5px;'>";
        echo "❌ <strong>فشل الاتصال بقاعدة البيانات:</strong><br>" . $e->getMessage();
        echo "</div>";
    }
}

// 5. فحص الملفات المطلوبة
echo "<h2>5. فحص الملفات المطلوبة</h2>";
$required_files = [
    '_init.php',
    'index.php',
    'functions.php',
    '_inc/lib/database.php',
    '_inc/helper/common.php'
];

foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "<p>✅ <strong>$file:</strong> موجود</p>";
    } else {
        echo "<p>❌ <strong>$file:</strong> غير موجود</p>";
    }
}

// 6. فحص صلاحيات المجلدات
echo "<h2>6. فحص صلاحيات المجلدات</h2>";
$writable_dirs = ['storage', 'storage/logs', 'storage/backups', 'storage/products'];

foreach ($writable_dirs as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "<p>✅ <strong>$dir:</strong> قابل للكتابة</p>";
        } else {
            echo "<p>❌ <strong>$dir:</strong> غير قابل للكتابة</p>";
        }
    } else {
        echo "<p>⚠️ <strong>$dir:</strong> المجلد غير موجود</p>";
    }
}

// 7. محاولة تحميل _init.php
echo "<h2>7. اختبار تحميل _init.php</h2>";
try {
    ob_start();
    include_once '_init.php';
    $output = ob_get_clean();
    
    echo "<div style='color: green; background: #d4edda; padding: 10px; border-radius: 5px;'>";
    echo "✅ <strong>تم تحميل _init.php بنجاح</strong>";
    echo "</div>";
    
    if (!empty($output)) {
        echo "<h3>مخرجات _init.php:</h3>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>" . htmlspecialchars($output) . "</pre>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red; background: #f8d7da; padding: 10px; border-radius: 5px;'>";
    echo "❌ <strong>خطأ في تحميل _init.php:</strong><br>" . $e->getMessage();
    echo "</div>";
} catch (Error $e) {
    echo "<div style='color: red; background: #f8d7da; padding: 10px; border-radius: 5px;'>";
    echo "❌ <strong>خطأ فادح في _init.php:</strong><br>" . $e->getMessage();
    echo "</div>";
}

// 8. فحص سجل الأخطاء
echo "<h2>8. سجل الأخطاء</h2>";
$error_log = ini_get('error_log');
if ($error_log && file_exists($error_log)) {
    echo "<p><strong>مسار سجل الأخطاء:</strong> $error_log</p>";
    $errors = file_get_contents($error_log);
    $recent_errors = array_slice(explode("\n", $errors), -10);
    echo "<h3>آخر 10 أخطاء:</h3>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; max-height: 200px; overflow-y: auto;'>";
    echo htmlspecialchars(implode("\n", $recent_errors));
    echo "</pre>";
} else {
    echo "<p>⚠️ لم يتم العثور على سجل الأخطاء</p>";
}

echo "<hr>";
echo "<h2>الحلول المقترحة:</h2>";
echo "<ol>";
echo "<li><strong>إذا كانت قاعدة البيانات فارغة:</strong> استورد ملف kidzrcle_MBEAAAT_database_full.sql</li>";
echo "<li><strong>إذا كانت الصلاحيات خاطئة:</strong> اضبط صلاحيات المجلدات إلى 755 والملفات إلى 644</li>";
echo "<li><strong>إذا كانت الإضافات مفقودة:</strong> اطلب من مزود الاستضافة تفعيلها</li>";
echo "<li><strong>إذا كان هناك خطأ في _init.php:</strong> تحقق من الكود والمسارات</li>";
echo "</ol>";

echo "<hr>";
echo "<p><small>تاريخ التشخيص: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 20px auto;
    padding: 20px;
    background-color: #f5f5f5;
}

h1, h2, h3 {
    color: #333;
}

p, li {
    line-height: 1.6;
}

pre {
    white-space: pre-wrap;
    word-wrap: break-word;
}
</style>
