<?php
/**
 * ملف اختبار الاتصال بقاعدة البيانات
 * استخدم هذا الملف للتأكد من صحة إعدادات قاعدة البيانات
 */

echo "<h2>اختبار الاتصال بقاعدة البيانات - نظام ModernPOS</h2>";

// إعدادات قاعدة البيانات
$host = 'localhost';
$dbname = 'kidzrcle_MBEAAAT';
$username = 'kidzrcle_MBEAAAT';
$password = 'kidzrcle_MBEAAAT';
$port = '3306';

echo "<h3>معلومات الاتصال:</h3>";
echo "<ul>";
echo "<li><strong>الخادم:</strong> $host</li>";
echo "<li><strong>قاعدة البيانات:</strong> $dbname</li>";
echo "<li><strong>اسم المستخدم:</strong> $username</li>";
echo "<li><strong>المنفذ:</strong> $port</li>";
echo "</ul>";

try {
    // محاولة الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div style='color: green; background: #d4edda; padding: 10px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>✅ نجح الاتصال بقاعدة البيانات!</strong>";
    echo "</div>";
    
    // التحقق من وجود الجداول
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h3>الجداول الموجودة في قاعدة البيانات:</h3>";
    echo "<p><strong>عدد الجداول:</strong> " . count($tables) . "</p>";
    
    if (count($tables) > 0) {
        echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<details>";
        echo "<summary>انقر لعرض قائمة الجداول</summary>";
        echo "<ul style='columns: 3; margin-top: 10px;'>";
        foreach ($tables as $table) {
            echo "<li>$table</li>";
        }
        echo "</ul>";
        echo "</details>";
        echo "</div>";
        
        // التحقق من وجود المستخدم الافتراضي
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = 'admin'");
        $stmt->execute();
        $adminExists = $stmt->fetchColumn();
        
        if ($adminExists > 0) {
            echo "<div style='color: green; background: #d4edda; padding: 10px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 10px 0;'>";
            echo "<strong>✅ المستخدم الافتراضي 'admin' موجود</strong>";
            echo "</div>";
        } else {
            echo "<div style='color: orange; background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 10px 0;'>";
            echo "<strong>⚠️ المستخدم الافتراضي 'admin' غير موجود</strong>";
            echo "</div>";
        }
        
        // التحقق من وجود المتجر الافتراضي
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM stores");
        $stmt->execute();
        $storeCount = $stmt->fetchColumn();
        
        if ($storeCount > 0) {
            echo "<div style='color: green; background: #d4edda; padding: 10px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 10px 0;'>";
            echo "<strong>✅ يوجد $storeCount متجر في النظام</strong>";
            echo "</div>";
        } else {
            echo "<div style='color: orange; background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 10px 0;'>";
            echo "<strong>⚠️ لا يوجد متاجر في النظام</strong>";
            echo "</div>";
        }
        
    } else {
        echo "<div style='color: red; background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0;'>";
        echo "<strong>❌ قاعدة البيانات فارغة! يجب استيراد ملف SQL</strong>";
        echo "</div>";
    }
    
} catch (PDOException $e) {
    echo "<div style='color: red; background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>❌ فشل الاتصال بقاعدة البيانات:</strong><br>";
    echo $e->getMessage();
    echo "</div>";
    
    echo "<h3>خطوات حل المشكلة:</h3>";
    echo "<ol>";
    echo "<li>تأكد من أن MySQL يعمل</li>";
    echo "<li>تحقق من صحة اسم المستخدم وكلمة المرور</li>";
    echo "<li>تأكد من وجود قاعدة البيانات '$dbname'</li>";
    echo "<li>تحقق من صلاحيات المستخدم '$username'</li>";
    echo "</ol>";
}

echo "<hr>";
echo "<h3>الخطوات التالية:</h3>";
echo "<ol>";
echo "<li>إذا كان الاتصال ناجحاً والجداول موجودة، يمكنك الآن الوصول للنظام</li>";
echo "<li>اذهب إلى الصفحة الرئيسية للموقع</li>";
echo "<li>سجل دخول بالمستخدم: <strong>admin</strong> وكلمة المرور: <strong>password</strong></li>";
echo "<li><strong>مهم:</strong> غيّر كلمة المرور فوراً بعد تسجيل الدخول</li>";
echo "</ol>";

echo "<hr>";
echo "<p><small>تاريخ الاختبار: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background-color: #f5f5f5;
}

h2, h3 {
    color: #333;
}

ul, ol {
    line-height: 1.6;
}

details {
    cursor: pointer;
}

summary {
    font-weight: bold;
    padding: 5px;
    background: #e9ecef;
    border-radius: 3px;
}
</style>
