<?php
/**
 * ملف اختبار بسيط لتشخيص المشكلة
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>اختبار بسيط لنظام ModernPOS</h1>";

// اختبار 1: PHP يعمل
echo "<h2>✅ PHP يعمل بشكل صحيح</h2>";
echo "<p>إصدار PHP: " . phpversion() . "</p>";
echo "<p>الوقت الحالي: " . date('Y-m-d H:i:s') . "</p>";

// اختبار 2: ملف config.php
echo "<h2>اختبار ملف config.php</h2>";
if (file_exists('config.php')) {
    echo "<p>✅ ملف config.php موجود</p>";
    
    try {
        require_once 'config.php';
        echo "<p>✅ تم تحميل config.php بنجاح</p>";
        
        if (defined('INSTALLED')) {
            echo "<p>✅ INSTALLED = " . (INSTALLED ? 'true' : 'false') . "</p>";
        }
        
        if (isset($sql_details)) {
            echo "<p>✅ إعدادات قاعدة البيانات موجودة</p>";
            echo "<ul>";
            echo "<li>Host: " . $sql_details['host'] . "</li>";
            echo "<li>Database: " . $sql_details['db'] . "</li>";
            echo "<li>User: " . $sql_details['user'] . "</li>";
            echo "</ul>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ خطأ في config.php: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p>❌ ملف config.php غير موجود</p>";
}

// اختبار 3: قاعدة البيانات
echo "<h2>اختبار قاعدة البيانات</h2>";
if (isset($sql_details)) {
    try {
        $pdo = new PDO(
            "mysql:host={$sql_details['host']};port={$sql_details['port']};dbname={$sql_details['db']};charset=utf8",
            $sql_details['user'],
            $sql_details['pass']
        );
        
        echo "<p>✅ الاتصال بقاعدة البيانات نجح</p>";
        
        // فحص الجداول
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "<p>عدد الجداول: " . count($tables) . "</p>";
        
        if (count($tables) > 0) {
            echo "<p>✅ قاعدة البيانات تحتوي على جداول</p>";
            
            // فحص جدول المستخدمين
            try {
                $stmt = $pdo->query("SELECT COUNT(*) FROM users");
                $userCount = $stmt->fetchColumn();
                echo "<p>عدد المستخدمين: $userCount</p>";
                
                if ($userCount > 0) {
                    $stmt = $pdo->query("SELECT username FROM users LIMIT 1");
                    $firstUser = $stmt->fetchColumn();
                    echo "<p>أول مستخدم: $firstUser</p>";
                }
                
            } catch (Exception $e) {
                echo "<p>⚠️ لا يمكن الوصول لجدول المستخدمين: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p>⚠️ قاعدة البيانات فارغة</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p>❌ فشل الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
    }
}

// اختبار 4: الملفات المطلوبة
echo "<h2>اختبار الملفات المطلوبة</h2>";
$files = ['_init.php', 'functions.php', '_inc/lib/database.php'];
foreach ($files as $file) {
    if (file_exists($file)) {
        echo "<p>✅ $file موجود</p>";
    } else {
        echo "<p>❌ $file غير موجود</p>";
    }
}

// اختبار 5: محاولة تحميل _init.php
echo "<h2>اختبار تحميل _init.php</h2>";
if (file_exists('_init.php')) {
    try {
        // تشغيل في buffer منفصل لتجنب المشاكل
        ob_start();
        include '_init.php';
        $output = ob_get_clean();
        
        echo "<p>✅ تم تحميل _init.php بنجاح</p>";
        
        if (!empty($output)) {
            echo "<h3>مخرجات _init.php:</h3>";
            echo "<pre>" . htmlspecialchars($output) . "</pre>";
        }
        
        // فحص المتغيرات المهمة
        if (isset($db)) {
            echo "<p>✅ متغير قاعدة البيانات \$db متوفر</p>";
        }
        
        if (isset($user)) {
            echo "<p>✅ متغير المستخدم \$user متوفر</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ خطأ في تحميل _init.php: " . $e->getMessage() . "</p>";
        echo "<p>الملف: " . $e->getFile() . "</p>";
        echo "<p>السطر: " . $e->getLine() . "</p>";
    } catch (Error $e) {
        echo "<p>❌ خطأ فادح في _init.php: " . $e->getMessage() . "</p>";
        echo "<p>الملف: " . $e->getFile() . "</p>";
        echo "<p>السطر: " . $e->getLine() . "</p>";
    }
}

echo "<hr>";
echo "<h2>الخطوات التالية:</h2>";
echo "<ol>";
echo "<li>إذا كانت جميع الاختبارات ناجحة، جرب الوصول للصفحة الرئيسية</li>";
echo "<li>إذا كانت قاعدة البيانات فارغة، استورد ملف SQL</li>";
echo "<li>إذا كان هناك خطأ في _init.php، تحقق من الكود</li>";
echo "<li>تحقق من سجلات أخطاء الخادم</li>";
echo "</ol>";

echo "<hr>";
echo "<p>تم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "</p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}
h1, h2 { color: #333; }
p { line-height: 1.6; }
pre { background: #f8f9fa; padding: 10px; border-radius: 5px; }
</style>
