<?php
/**
 * تشخيص متقدم لمشكلة HTTP 500
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>تشخيص متقدم لنظام ModernPOS</h1>";

// 1. فحص قاعدة البيانات بالتفصيل
echo "<h2>1. فحص قاعدة البيانات المفصل</h2>";

try {
    require_once 'config.php';
    
    $pdo = new PDO(
        "mysql:host={$sql_details['host']};port={$sql_details['port']};dbname={$sql_details['db']};charset=utf8",
        $sql_details['user'],
        $sql_details['pass']
    );
    
    echo "<p>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // فحص الجداول المطلوبة
    $required_tables = [
        'languages', 'language_text', 'sessions', 'settings_general',
        'users', 'user_groups', 'stores', 'user_to_store',
        'currency', 'currency_to_store', 'units', 'unit_to_store',
        'categorys', 'category_to_store', 'taxrates', 'taxrate_to_store'
    ];
    
    echo "<h3>الجداول المطلوبة:</h3>";
    $missing_tables = [];
    
    foreach ($required_tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
            $count = $stmt->fetchColumn();
            echo "<p>✅ <strong>$table:</strong> موجود ($count صف)</p>";
        } catch (Exception $e) {
            echo "<p>❌ <strong>$table:</strong> مفقود أو خطأ</p>";
            $missing_tables[] = $table;
        }
    }
    
    if (!empty($missing_tables)) {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>الجداول المفقودة:</h3>";
        echo "<ul>";
        foreach ($missing_tables as $table) {
            echo "<li>$table</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    // فحص المستخدمين
    try {
        $stmt = $pdo->query("SELECT username, email, status FROM users");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>المستخدمين الموجودين:</h3>";
        if (empty($users)) {
            echo "<p>❌ لا يوجد مستخدمين</p>";
        } else {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>اسم المستخدم</th><th>البريد الإلكتروني</th><th>الحالة</th></tr>";
            foreach ($users as $user) {
                echo "<tr>";
                echo "<td>{$user['username']}</td>";
                echo "<td>{$user['email']}</td>";
                echo "<td>" . ($user['status'] ? 'نشط' : 'غير نشط') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<p>❌ خطأ في فحص المستخدمين: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

// 2. فحص ملف _init.php بالتفصيل
echo "<h2>2. فحص ملف _init.php بالتفصيل</h2>";

if (file_exists('_init.php')) {
    echo "<p>✅ ملف _init.php موجود</p>";
    
    // قراءة محتوى الملف
    $init_content = file_get_contents('_init.php');
    $lines = explode("\n", $init_content);
    
    echo "<p><strong>عدد الأسطر:</strong> " . count($lines) . "</p>";
    
    // البحث عن الأسطر المهمة
    $important_lines = [];
    foreach ($lines as $line_num => $line) {
        if (strpos($line, 'languages') !== false || 
            strpos($line, 'Database') !== false ||
            strpos($line, 'PDO') !== false) {
            $important_lines[] = ($line_num + 1) . ": " . trim($line);
        }
    }
    
    if (!empty($important_lines)) {
        echo "<h3>الأسطر المهمة في _init.php:</h3>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
        foreach ($important_lines as $line) {
            echo htmlspecialchars($line) . "\n";
        }
        echo "</pre>";
    }
    
    // محاولة تحميل _init.php خطوة بخطوة
    echo "<h3>محاولة تحميل _init.php:</h3>";
    
    try {
        // تشغيل في buffer منفصل
        ob_start();
        
        // تعطيل exit و die مؤقتاً
        $old_error_handler = set_error_handler(function($severity, $message, $file, $line) {
            throw new ErrorException($message, 0, $severity, $file, $line);
        });
        
        include '_init.php';
        
        restore_error_handler();
        $output = ob_get_clean();
        
        echo "<p>✅ تم تحميل _init.php بنجاح</p>";
        
        if (!empty($output)) {
            echo "<h4>مخرجات _init.php:</h4>";
            echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; max-height: 200px; overflow-y: auto;'>";
            echo htmlspecialchars($output);
            echo "</pre>";
        }
        
        // فحص المتغيرات المهمة
        if (isset($db)) {
            echo "<p>✅ متغير \$db متوفر</p>";
        } else {
            echo "<p>❌ متغير \$db غير متوفر</p>";
        }
        
        if (isset($user)) {
            echo "<p>✅ متغير \$user متوفر</p>";
        } else {
            echo "<p>❌ متغير \$user غير متوفر</p>";
        }
        
        if (isset($language)) {
            echo "<p>✅ متغير \$language متوفر</p>";
        } else {
            echo "<p>❌ متغير \$language غير متوفر</p>";
        }
        
    } catch (Exception $e) {
        ob_end_clean();
        echo "<p>❌ خطأ في تحميل _init.php:</p>";
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px;'>";
        echo "<strong>الرسالة:</strong> " . $e->getMessage() . "<br>";
        echo "<strong>الملف:</strong> " . $e->getFile() . "<br>";
        echo "<strong>السطر:</strong> " . $e->getLine() . "<br>";
        echo "</div>";
        
        // محاولة تحديد السطر المشكل
        $error_line = $e->getLine();
        if ($error_line > 0 && $error_line <= count($lines)) {
            echo "<h4>السطر المشكل:</h4>";
            echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 5px;'>";
            echo "السطر $error_line: " . htmlspecialchars($lines[$error_line - 1]);
            echo "</pre>";
        }
    }
}

// 3. إنشاء ملف _init.php مبسط للاختبار
echo "<h2>3. إنشاء ملف _init.php مبسط</h2>";

$simple_init = '<?php
// ملف _init.php مبسط للاختبار
error_reporting(E_ALL);
ini_set("display_errors", 1);

// تحميل config.php
require_once "config.php";

// الاتصال بقاعدة البيانات
try {
    $db = new PDO(
        "mysql:host={$sql_details[\'host\']};port={$sql_details[\'port\']};dbname={$sql_details[\'db\']};charset=utf8",
        $sql_details[\'user\'],
        $sql_details[\'pass\']
    );
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // فحص جدول languages
    try {
        $stmt = $db->query("SELECT COUNT(*) FROM languages");
        $count = $stmt->fetchColumn();
        // echo "Languages table has $count rows\\n";
    } catch (Exception $e) {
        // إنشاء جدول languages إذا لم يكن موجوداً
        $db->exec("CREATE TABLE IF NOT EXISTS `languages` (
            `language_id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(32) NOT NULL,
            `code` varchar(5) NOT NULL,
            `locale` varchar(255) NOT NULL,
            `image` varchar(64) NOT NULL,
            `directory` varchar(3) NOT NULL,
            `sort_order` int(3) NOT NULL DEFAULT 0,
            `status` tinyint(1) NOT NULL,
            PRIMARY KEY (`language_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8");
        
        // إدراج اللغة الافتراضية
        $db->exec("INSERT IGNORE INTO `languages` VALUES (1, \'English\', \'en-gb\', \'en_US.UTF-8\', \'gb.png\', \'ltr\', 1, 1)");
    }
    
    // تعريف دالة db()
    function db() {
        global $db;
        return $db;
    }
    
    echo "<!-- _init.php loaded successfully -->";
    
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage());
}
?>';

file_put_contents('_init_simple.php', $simple_init);
echo "<p>✅ تم إنشاء ملف _init_simple.php</p>";

// 4. اختبار الملف المبسط
echo "<h2>4. اختبار الملف المبسط</h2>";

try {
    ob_start();
    include '_init_simple.php';
    $output = ob_get_clean();
    
    echo "<p>✅ الملف المبسط يعمل بنجاح</p>";
    
    if (!empty($output)) {
        echo "<p><strong>المخرجات:</strong> " . htmlspecialchars($output) . "</p>";
    }
    
} catch (Exception $e) {
    ob_end_clean();
    echo "<p>❌ الملف المبسط فشل: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h2>الحلول المقترحة:</h2>";
echo "<ol>";
echo "<li><strong>إذا كانت الجداول مفقودة:</strong> استورد ملف missing_tables.sql مرة أخرى</li>";
echo "<li><strong>إذا كان _init.php يفشل:</strong> استبدله بـ _init_simple.php مؤقتاً</li>";
echo "<li><strong>إذا كانت المشكلة في الكود:</strong> تحقق من السطر المشكل</li>";
echo "<li><strong>إذا استمرت المشكلة:</strong> جرب إعادة رفع جميع الملفات</li>";
echo "</ol>";

echo "<hr>";
echo "<p>تاريخ التشخيص: " . date('Y-m-d H:i:s') . "</p>";
?>

<style>
body { font-family: Arial, sans-serif; max-width: 1000px; margin: 20px auto; padding: 20px; }
h1, h2, h3 { color: #333; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
pre { white-space: pre-wrap; word-wrap: break-word; }
</style>
