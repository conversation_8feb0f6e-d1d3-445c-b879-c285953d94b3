<?php
/**
 * ملف index.php مبسط للاختبار
 * استخدم هذا الملف لتجاوز مشكلة HTTP 500 مؤقتاً
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

// بدء الجلسة
session_start();

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>نظام ModernPOS - تسجيل الدخول</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 20px; }";
echo ".container { max-width: 400px; margin: 50px auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }";
echo ".logo { text-align: center; margin-bottom: 30px; }";
echo ".form-group { margin-bottom: 20px; }";
echo "label { display: block; margin-bottom: 5px; font-weight: bold; }";
echo "input[type='text'], input[type='password'] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; box-sizing: border-box; }";
echo "button { width: 100%; padding: 12px; background: #007bff; color: white; border: none; border-radius: 5px; font-size: 16px; cursor: pointer; }";
echo "button:hover { background: #0056b3; }";
echo ".alert { padding: 10px; margin-bottom: 20px; border-radius: 5px; }";
echo ".alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }";
echo ".alert-danger { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }";
echo ".alert-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<div class='logo'>";
echo "<h1>🏪 نظام ModernPOS</h1>";
echo "<p>نظام نقاط البيع المتطور</p>";
echo "</div>";

// معالجة تسجيل الدخول
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['login'])) {
    $username = trim($_POST['username'] ?? '');
    $password = trim($_POST['password'] ?? '');
    
    if (empty($username) || empty($password)) {
        $error = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        try {
            // تحميل إعدادات قاعدة البيانات
            require_once 'config.php';
            
            // الاتصال بقاعدة البيانات
            $pdo = new PDO(
                "mysql:host={$sql_details['host']};port={$sql_details['port']};dbname={$sql_details['db']};charset=utf8",
                $sql_details['user'],
                $sql_details['pass']
            );
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // البحث عن المستخدم
            $stmt = $pdo->prepare("SELECT user_id, username, email, password, group_id, status FROM users WHERE username = ? AND status = 1");
            $stmt->execute([$username]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user && password_verify($password, $user['password'])) {
                // تسجيل الدخول ناجح
                $_SESSION['user_id'] = $user['user_id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['email'] = $user['email'];
                $_SESSION['group_id'] = $user['group_id'];
                $_SESSION['logged_in'] = true;
                
                $success = 'تم تسجيل الدخول بنجاح! جاري التوجيه...';
                
                // توجيه إلى لوحة التحكم
                echo "<script>";
                echo "setTimeout(function() { window.location.href = 'admin/dashboard.php'; }, 2000);";
                echo "</script>";
                
            } else {
                $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
            }
            
        } catch (Exception $e) {
            $error = 'خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage();
        }
    }
}

// عرض الرسائل
if (!empty($error)) {
    echo "<div class='alert alert-danger'>❌ $error</div>";
}

if (!empty($success)) {
    echo "<div class='alert alert-success'>✅ $success</div>";
}

// فحص حالة النظام
echo "<div class='alert alert-info'>";
echo "<strong>حالة النظام:</strong><br>";

try {
    require_once 'config.php';
    
    $pdo = new PDO(
        "mysql:host={$sql_details['host']};port={$sql_details['port']};dbname={$sql_details['db']};charset=utf8",
        $sql_details['user'],
        $sql_details['pass']
    );
    
    echo "✅ الاتصال بقاعدة البيانات: نجح<br>";
    
    // فحص المستخدمين
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE status = 1");
    $userCount = $stmt->fetchColumn();
    echo "✅ عدد المستخدمين النشطين: $userCount<br>";
    
    // فحص الجداول
    $stmt = $pdo->query("SHOW TABLES");
    $tableCount = $stmt->rowCount();
    echo "✅ عدد الجداول: $tableCount<br>";
    
    if ($userCount == 0) {
        echo "<br><strong>⚠️ تحذير:</strong> لا يوجد مستخدمين نشطين. يجب إضافة مستخدم admin.";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في النظام: " . $e->getMessage();
}

echo "</div>";

// نموذج تسجيل الدخول
if (empty($success)) {
    echo "<form method='POST'>";
    echo "<div class='form-group'>";
    echo "<label for='username'>اسم المستخدم:</label>";
    echo "<input type='text' id='username' name='username' value='" . htmlspecialchars($_POST['username'] ?? 'admin') . "' required>";
    echo "</div>";
    
    echo "<div class='form-group'>";
    echo "<label for='password'>كلمة المرور:</label>";
    echo "<input type='password' id='password' name='password' value='" . htmlspecialchars($_POST['password'] ?? 'password') . "' required>";
    echo "</div>";
    
    echo "<button type='submit' name='login'>تسجيل الدخول</button>";
    echo "</form>";
    
    echo "<div style='margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px; font-size: 14px;'>";
    echo "<strong>بيانات الدخول الافتراضية:</strong><br>";
    echo "اسم المستخدم: <code>admin</code><br>";
    echo "كلمة المرور: <code>password</code>";
    echo "</div>";
}

echo "</div>";

// روابط مفيدة
echo "<div style='text-align: center; margin-top: 20px;'>";
echo "<p><a href='test_simple.php'>اختبار النظام</a> | ";
echo "<a href='advanced_debug.php'>تشخيص متقدم</a> | ";
echo "<a href='debug_error.php'>تشخيص الأخطاء</a></p>";
echo "</div>";

echo "</body>";
echo "</html>";
?>
