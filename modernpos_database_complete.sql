-- ملف قاعدة البيانات الكامل لنظام ModernPOS
-- تم إنشاؤه في: 2025-01-01
-- اسم قاعدة البيانات: modernpos
-- يحتوي على جميع الجداول والبيانات الأساسية

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS `modernpos` DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;
USE `modernpos`;

-- --------------------------------------------------------
-- هيكل الجدول `stores` - المتاجر
-- --------------------------------------------------------

DROP TABLE IF EXISTS `stores`;
CREATE TABLE `stores` (
  `store_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `code_name` varchar(100) DEFAULT NULL,
  `mobile` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `country` varchar(100) DEFAULT NULL,
  `vat_reg_no` varchar(50) DEFAULT NULL,
  `zip_code` varchar(20) DEFAULT NULL,
  `cashier_id` int(11) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `logo` varchar(255) DEFAULT NULL,
  `favicon` varchar(255) DEFAULT NULL,
  `sound_effect` varchar(255) DEFAULT NULL,
  `status` tinyint(1) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  `receipt_printer` varchar(255) DEFAULT NULL,
  `remote_printing` varchar(255) DEFAULT NULL,
  `auto_print` tinyint(1) DEFAULT 0,
  `currency` varchar(10) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `users` - المستخدمين
-- --------------------------------------------------------

DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `user_id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `mobile` varchar(20) DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `group_id` int(11) NOT NULL,
  `dob` date DEFAULT NULL,
  `user_image` varchar(255) DEFAULT NULL,
  `status` tinyint(1) DEFAULT 1,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `user_groups` - مجموعات المستخدمين
-- --------------------------------------------------------

DROP TABLE IF EXISTS `user_groups`;
CREATE TABLE `user_groups` (
  `group_id` int(11) NOT NULL AUTO_INCREMENT,
  `group_name` varchar(100) NOT NULL,
  `permissions` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `user_to_store` - ربط المستخدمين بالمتاجر
-- --------------------------------------------------------

DROP TABLE IF EXISTS `user_to_store`;
CREATE TABLE `user_to_store` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `customers` - العملاء
-- --------------------------------------------------------

DROP TABLE IF EXISTS `customers`;
CREATE TABLE `customers` (
  `customer_id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_name` varchar(255) NOT NULL,
  `dob` date DEFAULT NULL,
  `customer_email` varchar(100) DEFAULT NULL,
  `customer_mobile` varchar(20) DEFAULT NULL,
  `customer_sex` enum('male','female') DEFAULT NULL,
  `customer_age` int(11) DEFAULT NULL,
  `gtin` varchar(50) DEFAULT NULL,
  `customer_address` text DEFAULT NULL,
  `customer_city` varchar(100) DEFAULT NULL,
  `customer_state` varchar(100) DEFAULT NULL,
  `customer_country` varchar(100) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`customer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `suppliers` - الموردين
-- --------------------------------------------------------

DROP TABLE IF EXISTS `suppliers`;
CREATE TABLE `suppliers` (
  `supplier_id` int(11) NOT NULL AUTO_INCREMENT,
  `supplier_name` varchar(255) NOT NULL,
  `supplier_email` varchar(100) DEFAULT NULL,
  `supplier_mobile` varchar(20) DEFAULT NULL,
  `supplier_address` text DEFAULT NULL,
  `supplier_city` varchar(100) DEFAULT NULL,
  `supplier_state` varchar(100) DEFAULT NULL,
  `supplier_country` varchar(100) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`supplier_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `categorys` - الفئات
-- --------------------------------------------------------

DROP TABLE IF EXISTS `categorys`;
CREATE TABLE `categorys` (
  `category_id` int(11) NOT NULL AUTO_INCREMENT,
  `category_name` varchar(255) NOT NULL,
  `category_slug` varchar(255) DEFAULT NULL,
  `parent_id` int(11) DEFAULT 0,
  `category_details` text DEFAULT NULL,
  `category_image` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `category_to_store` - ربط الفئات بالمتاجر
-- --------------------------------------------------------

DROP TABLE IF EXISTS `category_to_store`;
CREATE TABLE `category_to_store` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ccategory_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `status` tinyint(1) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `ccategory_id` (`ccategory_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `brands` - العلامات التجارية
-- --------------------------------------------------------

DROP TABLE IF EXISTS `brands`;
CREATE TABLE `brands` (
  `brand_id` int(11) NOT NULL AUTO_INCREMENT,
  `brand_name` varchar(255) NOT NULL,
  `code_name` varchar(100) DEFAULT NULL,
  `brand_details` text DEFAULT NULL,
  `brand_image` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`brand_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `brand_to_store` - ربط العلامات التجارية بالمتاجر
-- --------------------------------------------------------

DROP TABLE IF EXISTS `brand_to_store`;
CREATE TABLE `brand_to_store` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `brand_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `status` tinyint(1) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `brand_id` (`brand_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `units` - الوحدات
-- --------------------------------------------------------

DROP TABLE IF EXISTS `units`;
CREATE TABLE `units` (
  `unit_id` int(11) NOT NULL AUTO_INCREMENT,
  `unit_name` varchar(100) NOT NULL,
  `code_name` varchar(50) DEFAULT NULL,
  `unit_details` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`unit_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `unit_to_store` - ربط الوحدات بالمتاجر
-- --------------------------------------------------------

DROP TABLE IF EXISTS `unit_to_store`;
CREATE TABLE `unit_to_store` (
  `unit2s_id` int(11) NOT NULL AUTO_INCREMENT,
  `uunit_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `status` tinyint(1) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  PRIMARY KEY (`unit2s_id`),
  KEY `uunit_id` (`uunit_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `currency` - العملات
-- --------------------------------------------------------

DROP TABLE IF EXISTS `currency`;
CREATE TABLE `currency` (
  `currency_id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL,
  `code` varchar(10) NOT NULL,
  `symbol_left` varchar(10) DEFAULT NULL,
  `symbol_right` varchar(10) DEFAULT NULL,
  `decimal_place` int(11) DEFAULT 2,
  `value` decimal(15,8) DEFAULT 1.00000000,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`currency_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `currency_to_store` - ربط العملات بالمتاجر
-- --------------------------------------------------------

DROP TABLE IF EXISTS `currency_to_store`;
CREATE TABLE `currency_to_store` (
  `ca2s_id` int(11) NOT NULL AUTO_INCREMENT,
  `currency_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `status` tinyint(1) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  PRIMARY KEY (`ca2s_id`),
  KEY `currency_id` (`currency_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `products` - المنتجات
-- --------------------------------------------------------

DROP TABLE IF EXISTS `products`;
CREATE TABLE `products` (
  `product_id` int(11) NOT NULL AUTO_INCREMENT,
  `p_type` varchar(50) DEFAULT 'general',
  `p_name` varchar(255) NOT NULL,
  `p_code` varchar(100) DEFAULT NULL,
  `hsn_code` varchar(50) DEFAULT NULL,
  `barcode_symbology` varchar(50) DEFAULT 'CODE128',
  `category_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `p_image` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`product_id`),
  KEY `category_id` (`category_id`),
  KEY `unit_id` (`unit_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `product_to_store` - ربط المنتجات بالمتاجر
-- --------------------------------------------------------

DROP TABLE IF EXISTS `product_to_store`;
CREATE TABLE `product_to_store` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `price` decimal(15,4) DEFAULT 0.0000,
  `purchase_price` decimal(15,4) DEFAULT 0.0000,
  `quantity` decimal(15,4) DEFAULT 0.0000,
  `alert_quantity` decimal(15,4) DEFAULT 0.0000,
  `tax_rate_id` int(11) DEFAULT NULL,
  `status` tinyint(1) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `taxrates` - معدلات الضرائب
-- --------------------------------------------------------

DROP TABLE IF EXISTS `taxrates`;
CREATE TABLE `taxrates` (
  `taxrate_id` int(11) NOT NULL AUTO_INCREMENT,
  `taxrate_name` varchar(100) NOT NULL,
  `taxrate_percent` decimal(15,4) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`taxrate_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `taxrate_to_store` - ربط معدلات الضرائب بالمتاجر
-- --------------------------------------------------------

DROP TABLE IF EXISTS `taxrate_to_store`;
CREATE TABLE `taxrate_to_store` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `taxrate_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `status` tinyint(1) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `taxrate_id` (`taxrate_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `selling_info` - معلومات المبيعات
-- --------------------------------------------------------

DROP TABLE IF EXISTS `selling_info`;
CREATE TABLE `selling_info` (
  `selling_id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_id` varchar(100) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `store_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `selling_date` date NOT NULL,
  `selling_time` time NOT NULL,
  `subtotal` decimal(25,4) DEFAULT 0.0000,
  `tax` decimal(25,4) DEFAULT 0.0000,
  `discount` decimal(25,4) DEFAULT 0.0000,
  `shipping` decimal(25,4) DEFAULT 0.0000,
  `grand_total` decimal(25,4) DEFAULT 0.0000,
  `paid_amount` decimal(25,4) DEFAULT 0.0000,
  `due_amount` decimal(25,4) DEFAULT 0.0000,
  `change_amount` decimal(25,4) DEFAULT 0.0000,
  `payment_method` varchar(50) DEFAULT NULL,
  `payment_status` varchar(20) DEFAULT 'pending',
  `note` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`selling_id`),
  UNIQUE KEY `invoice_id` (`invoice_id`),
  KEY `customer_id` (`customer_id`),
  KEY `store_id` (`store_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `selling_item` - عناصر المبيعات
-- --------------------------------------------------------

DROP TABLE IF EXISTS `selling_item`;
CREATE TABLE `selling_item` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `selling_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `unit_price` decimal(15,4) NOT NULL,
  `total_price` decimal(15,4) NOT NULL,
  `tax_amount` decimal(15,4) DEFAULT 0.0000,
  `discount_amount` decimal(15,4) DEFAULT 0.0000,
  PRIMARY KEY (`id`),
  KEY `selling_id` (`selling_id`),
  KEY `product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `selling_price` - أسعار المبيعات
-- --------------------------------------------------------

DROP TABLE IF EXISTS `selling_price`;
CREATE TABLE `selling_price` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `selling_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `subtotal` decimal(25,4) DEFAULT 0.0000,
  `tax` decimal(25,4) DEFAULT 0.0000,
  `discount` decimal(25,4) DEFAULT 0.0000,
  `shipping` decimal(25,4) DEFAULT 0.0000,
  `grand_total` decimal(25,4) DEFAULT 0.0000,
  PRIMARY KEY (`id`),
  KEY `selling_id` (`selling_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `purchase_info` - معلومات المشتريات
-- --------------------------------------------------------

DROP TABLE IF EXISTS `purchase_info`;
CREATE TABLE `purchase_info` (
  `purchase_id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_id` varchar(100) NOT NULL,
  `supplier_id` int(11) DEFAULT NULL,
  `store_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `purchase_date` date NOT NULL,
  `purchase_time` time NOT NULL,
  `subtotal` decimal(25,4) DEFAULT 0.0000,
  `tax` decimal(25,4) DEFAULT 0.0000,
  `discount` decimal(25,4) DEFAULT 0.0000,
  `shipping` decimal(25,4) DEFAULT 0.0000,
  `grand_total` decimal(25,4) DEFAULT 0.0000,
  `paid_amount` decimal(25,4) DEFAULT 0.0000,
  `due_amount` decimal(25,4) DEFAULT 0.0000,
  `payment_method` varchar(50) DEFAULT NULL,
  `payment_status` varchar(20) DEFAULT 'pending',
  `note` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`purchase_id`),
  UNIQUE KEY `invoice_id` (`invoice_id`),
  KEY `supplier_id` (`supplier_id`),
  KEY `store_id` (`store_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `purchase_item` - عناصر المشتريات
-- --------------------------------------------------------

DROP TABLE IF EXISTS `purchase_item`;
CREATE TABLE `purchase_item` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `purchase_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `unit_price` decimal(15,4) NOT NULL,
  `total_price` decimal(15,4) NOT NULL,
  `tax_amount` decimal(15,4) DEFAULT 0.0000,
  `discount_amount` decimal(15,4) DEFAULT 0.0000,
  PRIMARY KEY (`id`),
  KEY `purchase_id` (`purchase_id`),
  KEY `product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `purchase_price` - أسعار المشتريات
-- --------------------------------------------------------

DROP TABLE IF EXISTS `purchase_price`;
CREATE TABLE `purchase_price` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `purchase_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `subtotal` decimal(25,4) DEFAULT 0.0000,
  `tax` decimal(25,4) DEFAULT 0.0000,
  `discount` decimal(25,4) DEFAULT 0.0000,
  `shipping` decimal(25,4) DEFAULT 0.0000,
  `grand_total` decimal(25,4) DEFAULT 0.0000,
  PRIMARY KEY (`id`),
  KEY `purchase_id` (`purchase_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `payments` - المدفوعات
-- --------------------------------------------------------

DROP TABLE IF EXISTS `payments`;
CREATE TABLE `payments` (
  `payment_id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_id` varchar(100) NOT NULL,
  `payment_type` enum('purchase','selling','expense','income') NOT NULL,
  `store_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `amount` decimal(25,4) NOT NULL,
  `payment_method` varchar(50) DEFAULT NULL,
  `payment_date` date NOT NULL,
  `payment_time` time NOT NULL,
  `note` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`payment_id`),
  KEY `store_id` (`store_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `purchase_payments` - مدفوعات المشتريات
-- --------------------------------------------------------

DROP TABLE IF EXISTS `purchase_payments`;
CREATE TABLE `purchase_payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `purchase_id` int(11) NOT NULL,
  `payment_id` int(11) NOT NULL,
  `amount` decimal(25,4) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `purchase_id` (`purchase_id`),
  KEY `payment_id` (`payment_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `returns` - المرتجعات
-- --------------------------------------------------------

DROP TABLE IF EXISTS `returns`;
CREATE TABLE `returns` (
  `return_id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_id` varchar(100) NOT NULL,
  `original_invoice_id` varchar(100) NOT NULL,
  `return_type` enum('purchase','selling') NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `supplier_id` int(11) DEFAULT NULL,
  `store_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `return_date` date NOT NULL,
  `return_time` time NOT NULL,
  `subtotal` decimal(25,4) DEFAULT 0.0000,
  `tax` decimal(25,4) DEFAULT 0.0000,
  `discount` decimal(25,4) DEFAULT 0.0000,
  `grand_total` decimal(25,4) DEFAULT 0.0000,
  `refund_amount` decimal(25,4) DEFAULT 0.0000,
  `note` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`return_id`),
  UNIQUE KEY `invoice_id` (`invoice_id`),
  KEY `customer_id` (`customer_id`),
  KEY `supplier_id` (`supplier_id`),
  KEY `store_id` (`store_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `return_items` - عناصر المرتجعات
-- --------------------------------------------------------

DROP TABLE IF EXISTS `return_items`;
CREATE TABLE `return_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `return_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `unit_price` decimal(15,4) NOT NULL,
  `total_price` decimal(15,4) NOT NULL,
  `tax_amount` decimal(15,4) DEFAULT 0.0000,
  `discount_amount` decimal(15,4) DEFAULT 0.0000,
  PRIMARY KEY (`id`),
  KEY `return_id` (`return_id`),
  KEY `product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `purchase_returns` - مرتجعات المشتريات
-- --------------------------------------------------------

DROP TABLE IF EXISTS `purchase_returns`;
CREATE TABLE `purchase_returns` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `return_id` int(11) NOT NULL,
  `purchase_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `return_id` (`return_id`),
  KEY `purchase_id` (`purchase_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `purchase_return_items` - عناصر مرتجعات المشتريات
-- --------------------------------------------------------

DROP TABLE IF EXISTS `purchase_return_items`;
CREATE TABLE `purchase_return_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `return_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `unit_price` decimal(15,4) NOT NULL,
  `total_price` decimal(15,4) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `return_id` (`return_id`),
  KEY `product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `expenses` - المصروفات
-- --------------------------------------------------------

DROP TABLE IF EXISTS `expenses`;
CREATE TABLE `expenses` (
  `expense_id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_id` varchar(100) NOT NULL,
  `expense_category_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `amount` decimal(25,4) NOT NULL,
  `expense_date` date NOT NULL,
  `expense_time` time NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`expense_id`),
  UNIQUE KEY `invoice_id` (`invoice_id`),
  KEY `expense_category_id` (`expense_category_id`),
  KEY `store_id` (`store_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `expense_categorys` - فئات المصروفات
-- --------------------------------------------------------

DROP TABLE IF EXISTS `expense_categorys`;
CREATE TABLE `expense_categorys` (
  `expense_category_id` int(11) NOT NULL AUTO_INCREMENT,
  `expense_category_name` varchar(255) NOT NULL,
  `expense_category_details` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`expense_category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `income_sources` - مصادر الدخل
-- --------------------------------------------------------

DROP TABLE IF EXISTS `income_sources`;
CREATE TABLE `income_sources` (
  `income_source_id` int(11) NOT NULL AUTO_INCREMENT,
  `income_source_name` varchar(255) NOT NULL,
  `income_source_details` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`income_source_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `quotation_info` - معلومات العروض
-- --------------------------------------------------------

DROP TABLE IF EXISTS `quotation_info`;
CREATE TABLE `quotation_info` (
  `quotation_id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_id` varchar(100) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `store_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `quotation_date` date NOT NULL,
  `quotation_time` time NOT NULL,
  `subtotal` decimal(25,4) DEFAULT 0.0000,
  `tax` decimal(25,4) DEFAULT 0.0000,
  `discount` decimal(25,4) DEFAULT 0.0000,
  `shipping` decimal(25,4) DEFAULT 0.0000,
  `grand_total` decimal(25,4) DEFAULT 0.0000,
  `status` varchar(20) DEFAULT 'pending',
  `note` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`quotation_id`),
  UNIQUE KEY `invoice_id` (`invoice_id`),
  KEY `customer_id` (`customer_id`),
  KEY `store_id` (`store_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `quotation_item` - عناصر العروض
-- --------------------------------------------------------

DROP TABLE IF EXISTS `quotation_item`;
CREATE TABLE `quotation_item` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `quotation_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `unit_price` decimal(15,4) NOT NULL,
  `total_price` decimal(15,4) NOT NULL,
  `tax_amount` decimal(15,4) DEFAULT 0.0000,
  `discount_amount` decimal(15,4) DEFAULT 0.0000,
  PRIMARY KEY (`id`),
  KEY `quotation_id` (`quotation_id`),
  KEY `product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `quotation_price` - أسعار العروض
-- --------------------------------------------------------

DROP TABLE IF EXISTS `quotation_price`;
CREATE TABLE `quotation_price` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `quotation_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `subtotal` decimal(25,4) DEFAULT 0.0000,
  `tax` decimal(25,4) DEFAULT 0.0000,
  `discount` decimal(25,4) DEFAULT 0.0000,
  `shipping` decimal(25,4) DEFAULT 0.0000,
  `grand_total` decimal(25,4) DEFAULT 0.0000,
  PRIMARY KEY (`id`),
  KEY `quotation_id` (`quotation_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `holding_info` - معلومات الطلبات المعلقة
-- --------------------------------------------------------

DROP TABLE IF EXISTS `holding_info`;
CREATE TABLE `holding_info` (
  `holding_id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_id` varchar(100) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `store_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `holding_date` date NOT NULL,
  `holding_time` time NOT NULL,
  `subtotal` decimal(25,4) DEFAULT 0.0000,
  `tax` decimal(25,4) DEFAULT 0.0000,
  `discount` decimal(25,4) DEFAULT 0.0000,
  `shipping` decimal(25,4) DEFAULT 0.0000,
  `grand_total` decimal(25,4) DEFAULT 0.0000,
  `status` varchar(20) DEFAULT 'holding',
  `note` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`holding_id`),
  UNIQUE KEY `invoice_id` (`invoice_id`),
  KEY `customer_id` (`customer_id`),
  KEY `store_id` (`store_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `holding_item` - عناصر الطلبات المعلقة
-- --------------------------------------------------------

DROP TABLE IF EXISTS `holding_item`;
CREATE TABLE `holding_item` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `holding_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `unit_price` decimal(15,4) NOT NULL,
  `total_price` decimal(15,4) NOT NULL,
  `tax_amount` decimal(15,4) DEFAULT 0.0000,
  `discount_amount` decimal(15,4) DEFAULT 0.0000,
  PRIMARY KEY (`id`),
  KEY `holding_id` (`holding_id`),
  KEY `product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `holding_price` - أسعار الطلبات المعلقة
-- --------------------------------------------------------

DROP TABLE IF EXISTS `holding_price`;
CREATE TABLE `holding_price` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `holding_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `subtotal` decimal(25,4) DEFAULT 0.0000,
  `tax` decimal(25,4) DEFAULT 0.0000,
  `discount` decimal(25,4) DEFAULT 0.0000,
  `shipping` decimal(25,4) DEFAULT 0.0000,
  `grand_total` decimal(25,4) DEFAULT 0.0000,
  PRIMARY KEY (`id`),
  KEY `holding_id` (`holding_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `transfer_items` - عناصر التحويلات
-- --------------------------------------------------------

DROP TABLE IF EXISTS `transfer_items`;
CREATE TABLE `transfer_items` (
  `transfer_id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `from_store_id` int(11) NOT NULL,
  `to_store_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `transfer_date` date NOT NULL,
  `transfer_time` time NOT NULL,
  `user_id` int(11) NOT NULL,
  `note` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`transfer_id`),
  KEY `product_id` (`product_id`),
  KEY `from_store_id` (`from_store_id`),
  KEY `to_store_id` (`to_store_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `bank_accounts` - الحسابات البنكية
-- --------------------------------------------------------

DROP TABLE IF EXISTS `bank_accounts`;
CREATE TABLE `bank_accounts` (
  `account_id` int(11) NOT NULL AUTO_INCREMENT,
  `account_name` varchar(255) NOT NULL,
  `account_number` varchar(100) DEFAULT NULL,
  `bank_name` varchar(255) DEFAULT NULL,
  `branch_name` varchar(255) DEFAULT NULL,
  `balance` decimal(25,4) DEFAULT 0.0000,
  `store_id` int(11) NOT NULL,
  `status` tinyint(1) DEFAULT 1,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`account_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `bank_transaction_info` - معلومات المعاملات البنكية
-- --------------------------------------------------------

DROP TABLE IF EXISTS `bank_transaction_info`;
CREATE TABLE `bank_transaction_info` (
  `transaction_id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_id` varchar(100) NOT NULL,
  `account_id` int(11) NOT NULL,
  `transaction_type` enum('deposit','withdraw','transfer') NOT NULL,
  `amount` decimal(25,4) NOT NULL,
  `transaction_date` date NOT NULL,
  `transaction_time` time NOT NULL,
  `store_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`transaction_id`),
  UNIQUE KEY `invoice_id` (`invoice_id`),
  KEY `account_id` (`account_id`),
  KEY `store_id` (`store_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `bank_transaction_price` - أسعار المعاملات البنكية
-- --------------------------------------------------------

DROP TABLE IF EXISTS `bank_transaction_price`;
CREATE TABLE `bank_transaction_price` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `amount` decimal(25,4) DEFAULT 0.0000,
  PRIMARY KEY (`id`),
  KEY `transaction_id` (`transaction_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `loans` - القروض
-- --------------------------------------------------------

DROP TABLE IF EXISTS `loans`;
CREATE TABLE `loans` (
  `loan_id` int(11) NOT NULL AUTO_INCREMENT,
  `loan_type` enum('given','taken') NOT NULL,
  `person_name` varchar(255) NOT NULL,
  `amount` decimal(25,4) NOT NULL,
  `interest_rate` decimal(5,2) DEFAULT 0.00,
  `loan_date` date NOT NULL,
  `due_date` date DEFAULT NULL,
  `store_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `status` varchar(20) DEFAULT 'active',
  `description` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`loan_id`),
  KEY `store_id` (`store_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `installment_orders` - طلبات الأقساط
-- --------------------------------------------------------

DROP TABLE IF EXISTS `installment_orders`;
CREATE TABLE `installment_orders` (
  `installment_id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_id` varchar(100) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `total_amount` decimal(25,4) NOT NULL,
  `down_payment` decimal(25,4) DEFAULT 0.0000,
  `installment_amount` decimal(25,4) NOT NULL,
  `number_of_installments` int(11) NOT NULL,
  `paid_installments` int(11) DEFAULT 0,
  `start_date` date NOT NULL,
  `status` varchar(20) DEFAULT 'active',
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`installment_id`),
  UNIQUE KEY `invoice_id` (`invoice_id`),
  KEY `customer_id` (`customer_id`),
  KEY `store_id` (`store_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `installment_payments` - مدفوعات الأقساط
-- --------------------------------------------------------

DROP TABLE IF EXISTS `installment_payments`;
CREATE TABLE `installment_payments` (
  `payment_id` int(11) NOT NULL AUTO_INCREMENT,
  `installment_id` int(11) NOT NULL,
  `payment_amount` decimal(25,4) NOT NULL,
  `payment_date` date NOT NULL,
  `payment_time` time NOT NULL,
  `store_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `note` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`payment_id`),
  KEY `installment_id` (`installment_id`),
  KEY `store_id` (`store_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `customer_transactions` - معاملات العملاء
-- --------------------------------------------------------

DROP TABLE IF EXISTS `customer_transactions`;
CREATE TABLE `customer_transactions` (
  `transaction_id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_id` int(11) NOT NULL,
  `transaction_type` enum('debit','credit') NOT NULL,
  `amount` decimal(25,4) NOT NULL,
  `transaction_date` date NOT NULL,
  `transaction_time` time NOT NULL,
  `store_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `reference_id` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`transaction_id`),
  KEY `customer_id` (`customer_id`),
  KEY `store_id` (`store_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `sell_logs` - سجلات المبيعات
-- --------------------------------------------------------

DROP TABLE IF EXISTS `sell_logs`;
CREATE TABLE `sell_logs` (
  `log_id` int(11) NOT NULL AUTO_INCREMENT,
  `selling_id` int(11) NOT NULL,
  `action` varchar(50) NOT NULL,
  `store_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `log_date` date NOT NULL,
  `log_time` time NOT NULL,
  `details` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`log_id`),
  KEY `selling_id` (`selling_id`),
  KEY `store_id` (`store_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `purchase_logs` - سجلات المشتريات
-- --------------------------------------------------------

DROP TABLE IF EXISTS `purchase_logs`;
CREATE TABLE `purchase_logs` (
  `log_id` int(11) NOT NULL AUTO_INCREMENT,
  `purchase_id` int(11) NOT NULL,
  `action` varchar(50) NOT NULL,
  `store_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `log_date` date NOT NULL,
  `log_time` time NOT NULL,
  `details` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`log_id`),
  KEY `purchase_id` (`purchase_id`),
  KEY `store_id` (`store_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `sms_schedule` - جدولة الرسائل النصية
-- --------------------------------------------------------

DROP TABLE IF EXISTS `sms_schedule`;
CREATE TABLE `sms_schedule` (
  `sms_id` int(11) NOT NULL AUTO_INCREMENT,
  `recipient` varchar(20) NOT NULL,
  `message` text NOT NULL,
  `schedule_date` date NOT NULL,
  `schedule_time` time NOT NULL,
  `status` varchar(20) DEFAULT 'pending',
  `store_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `sent_at` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`sms_id`),
  KEY `store_id` (`store_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `pos_register` - سجل نقاط البيع
-- --------------------------------------------------------

DROP TABLE IF EXISTS `pos_register`;
CREATE TABLE `pos_register` (
  `register_id` int(11) NOT NULL AUTO_INCREMENT,
  `store_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `opening_balance` decimal(25,4) DEFAULT 0.0000,
  `closing_balance` decimal(25,4) DEFAULT 0.0000,
  `total_sales` decimal(25,4) DEFAULT 0.0000,
  `total_cash` decimal(25,4) DEFAULT 0.0000,
  `total_card` decimal(25,4) DEFAULT 0.0000,
  `opened_at` datetime NOT NULL,
  `closed_at` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT 'open',
  PRIMARY KEY (`register_id`),
  KEY `store_id` (`store_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `giftcards` - بطاقات الهدايا
-- --------------------------------------------------------

DROP TABLE IF EXISTS `giftcards`;
CREATE TABLE `giftcards` (
  `giftcard_id` int(11) NOT NULL AUTO_INCREMENT,
  `card_number` varchar(100) NOT NULL,
  `card_value` decimal(25,4) NOT NULL,
  `balance` decimal(25,4) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `store_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `expiry_date` date DEFAULT NULL,
  `status` varchar(20) DEFAULT 'active',
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`giftcard_id`),
  UNIQUE KEY `card_number` (`card_number`),
  KEY `customer_id` (`customer_id`),
  KEY `store_id` (`store_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `payment_methods` - طرق الدفع
-- --------------------------------------------------------

DROP TABLE IF EXISTS `payment_methods`;
CREATE TABLE `payment_methods` (
  `method_id` int(11) NOT NULL AUTO_INCREMENT,
  `method_name` varchar(100) NOT NULL,
  `method_code` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  `status` tinyint(1) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`method_id`),
  UNIQUE KEY `method_code` (`method_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- هيكل الجدول `settings` - إعدادات النظام
-- --------------------------------------------------------

DROP TABLE IF EXISTS `settings`;
CREATE TABLE `settings` (
  `setting_id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `store_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`setting_id`),
  UNIQUE KEY `setting_key_store` (`setting_key`, `store_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------
-- إدراج البيانات الأساسية
-- --------------------------------------------------------

-- إدراج مجموعة المستخدمين الافتراضية
INSERT INTO `user_groups` (`group_id`, `group_name`, `permissions`, `created_at`) VALUES
(1, 'Administrator', 'all', NOW()),
(2, 'Manager', 'limited', NOW()),
(3, 'Cashier', 'basic', NOW());

-- إدراج المستخدم الافتراضي (admin)
INSERT INTO `users` (`user_id`, `username`, `email`, `mobile`, `password`, `group_id`, `dob`, `user_image`, `status`, `created_at`) VALUES
(1, 'admin', '<EMAIL>', '1234567890', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1, '1990-01-01', NULL, 1, NOW());

-- إدراج المتجر الافتراضي
INSERT INTO `stores` (`store_id`, `name`, `code_name`, `mobile`, `email`, `country`, `vat_reg_no`, `zip_code`, `cashier_id`, `address`, `logo`, `favicon`, `sound_effect`, `status`, `sort_order`, `receipt_printer`, `remote_printing`, `auto_print`, `currency`, `created_at`) VALUES
(1, 'Modern POS Store', 'MAIN', '1234567890', '<EMAIL>', 'Saudi Arabia', '', '12345', 1, 'Main Street, City', NULL, NULL, NULL, 1, 1, NULL, NULL, 0, 'SAR', NOW());

-- ربط المستخدم بالمتجر
INSERT INTO `user_to_store` (`user_id`, `store_id`) VALUES (1, 1);

-- إدراج العملة الافتراضية
INSERT INTO `currency` (`currency_id`, `title`, `code`, `symbol_left`, `symbol_right`, `decimal_place`, `value`, `created_at`) VALUES
(1, 'Saudi Riyal', 'SAR', 'ر.س', '', 2, 1.00000000, NOW()),
(2, 'US Dollar', 'USD', '$', '', 2, 3.75000000, NOW());

-- ربط العملة بالمتجر
INSERT INTO `currency_to_store` (`currency_id`, `store_id`, `status`, `sort_order`) VALUES
(1, 1, 1, 1),
(2, 1, 1, 2);

-- إدراج الوحدات الافتراضية
INSERT INTO `units` (`unit_id`, `unit_name`, `code_name`, `unit_details`, `created_at`) VALUES
(1, 'Piece', 'PCS', 'Individual pieces', NOW()),
(2, 'Kilogram', 'KG', 'Weight in kilograms', NOW()),
(3, 'Liter', 'L', 'Volume in liters', NOW()),
(4, 'Meter', 'M', 'Length in meters', NOW());

-- ربط الوحدات بالمتجر
INSERT INTO `unit_to_store` (`uunit_id`, `store_id`, `status`, `sort_order`) VALUES
(1, 1, 1, 1),
(2, 1, 1, 2),
(3, 1, 1, 3),
(4, 1, 1, 4);

-- إدراج الفئات الافتراضية
INSERT INTO `categorys` (`category_id`, `category_name`, `category_slug`, `parent_id`, `category_details`, `category_image`, `created_at`) VALUES
(1, 'General', 'general', 0, 'General category for all products', NULL, NOW()),
(2, 'Electronics', 'electronics', 0, 'Electronic devices and accessories', NULL, NOW()),
(3, 'Clothing', 'clothing', 0, 'Clothes and fashion items', NULL, NOW()),
(4, 'Food & Beverages', 'food-beverages', 0, 'Food and drink items', NULL, NOW());

-- ربط الفئات بالمتجر
INSERT INTO `category_to_store` (`ccategory_id`, `store_id`, `status`, `sort_order`) VALUES
(1, 1, 1, 1),
(2, 1, 1, 2),
(3, 1, 1, 3),
(4, 1, 1, 4);

-- إدراج معدلات الضرائب الافتراضية
INSERT INTO `taxrates` (`taxrate_id`, `taxrate_name`, `taxrate_percent`, `created_at`) VALUES
(1, 'VAT 15%', 15.0000, NOW()),
(2, 'No Tax', 0.0000, NOW());

-- ربط معدلات الضرائب بالمتجر
INSERT INTO `taxrate_to_store` (`taxrate_id`, `store_id`, `status`, `sort_order`) VALUES
(1, 1, 1, 1),
(2, 1, 1, 2);

-- إدراج طرق الدفع الافتراضية
INSERT INTO `payment_methods` (`method_id`, `method_name`, `method_code`, `description`, `status`, `sort_order`, `created_at`) VALUES
(1, 'Cash', 'cash', 'Cash payment', 1, 1, NOW()),
(2, 'Credit Card', 'credit_card', 'Credit card payment', 1, 2, NOW()),
(3, 'Debit Card', 'debit_card', 'Debit card payment', 1, 3, NOW()),
(4, 'Bank Transfer', 'bank_transfer', 'Bank transfer payment', 1, 4, NOW());

-- إدراج فئات المصروفات الافتراضية
INSERT INTO `expense_categorys` (`expense_category_id`, `expense_category_name`, `expense_category_details`, `created_at`) VALUES
(1, 'Office Supplies', 'Office supplies and stationery', NOW()),
(2, 'Utilities', 'Electricity, water, internet bills', NOW()),
(3, 'Marketing', 'Advertising and promotional expenses', NOW()),
(4, 'Transportation', 'Vehicle and transportation costs', NOW());

-- إدراج مصادر الدخل الافتراضية
INSERT INTO `income_sources` (`income_source_id`, `income_source_name`, `income_source_details`, `created_at`) VALUES
(1, 'Product Sales', 'Revenue from product sales', NOW()),
(2, 'Service Income', 'Revenue from services', NOW()),
(3, 'Other Income', 'Miscellaneous income sources', NOW());

COMMIT;
